<template>
  <div class="markdown-test-page">
    <div class="page-header">
      <h1>MarkdownViewer 组件测试</h1>
      <p>测试Markdown渲染功能和样式效果</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-button @click="loadSampleContent">加载示例内容</a-button>
        <a-button @click="toggleLoading">切换加载状态</a-button>
        <a-button @click="clearContent">清空内容</a-button>
      </a-space>
    </div>

    <div class="test-content">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="Markdown源码" size="small">
            <a-textarea
              v-model:value="markdownContent"
              :rows="20"
              placeholder="请输入Markdown内容..."
              style="font-family: monospace;"
            />
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="渲染效果" size="small">
            <MarkdownViewer
              :content="markdownContent"
              :loading="isLoading"
              @content-rendered="onContentRendered"
              @render-error="onRenderError"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="test-info" v-if="renderInfo">
      <a-alert
        :message="renderInfo.type === 'success' ? '渲染成功' : '渲染失败'"
        :description="renderInfo.message"
        :type="renderInfo.type"
        show-icon
        closable
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MarkdownViewer } from '@/components'

// 响应式数据
const markdownContent = ref('')
const isLoading = ref(false)
const renderInfo = ref<{
  type: 'success' | 'error'
  message: string
} | null>(null)

// 示例Markdown内容
const sampleContent = `# MarkdownViewer 组件测试

## 功能特性

### 基础语法支持
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 列表支持
1. 有序列表项1
2. 有序列表项2
   - 嵌套无序列表
   - 另一个嵌套项

### 代码块高亮
\`\`\`javascript
// JavaScript代码示例
function testMarkdown() {
  console.log('MarkdownViewer组件测试');
  return {
    status: 'success',
    message: '渲染完成'
  };
}

const result = testMarkdown();
\`\`\`

\`\`\`python
# Python代码示例
def test_markdown():
    print("MarkdownViewer组件测试")
    return {
        "status": "success",
        "message": "渲染完成"
    }

result = test_markdown()
\`\`\`

### 表格支持
| 功能 | 状态 | 备注 |
|------|------|------|
| 标题渲染 | ✅ | 支持H1-H6 |
| 列表渲染 | ✅ | 有序和无序 |
| 代码高亮 | ✅ | 多语言支持 |
| 表格样式 | ✅ | 响应式设计 |

### 引用块
> 这是一个引用块示例
> 
> 支持多行引用内容
> 
> > 嵌套引用也是支持的

### 链接和图片
- [Vue.js官网](https://vuejs.org/)
- [Ant Design Vue](https://antdv.com/)

### 分割线
---

### 任务列表
- [x] 完成MarkdownViewer组件开发
- [x] 实现代码高亮功能
- [x] 添加表格样式优化
- [ ] 添加更多测试用例
- [ ] 性能优化

## 技术实现
使用 \`markdown-it\` 库进行Markdown解析，\`highlight.js\` 进行代码高亮。

### 安全性
组件已实现XSS防护，确保渲染内容的安全性。
`

// 方法
const loadSampleContent = () => {
  markdownContent.value = sampleContent
  renderInfo.value = null
}

const toggleLoading = () => {
  isLoading.value = !isLoading.value
}

const clearContent = () => {
  markdownContent.value = ''
  renderInfo.value = null
}

const onContentRendered = (html: string) => {
  renderInfo.value = {
    type: 'success',
    message: `内容渲染成功，生成HTML长度: ${html.length} 字符`
  }
}

const onRenderError = (error: string) => {
  renderInfo.value = {
    type: 'error',
    message: `渲染失败: ${error}`
  }
}

// 初始化加载示例内容
loadSampleContent()
</script>

<style scoped>
.markdown-test-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  margin-bottom: 8px;
  color: #1f2328;
}

.page-header p {
  color: #656d76;
  font-size: 16px;
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.test-content {
  margin-bottom: 24px;
}

.test-info {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-test-page {
    padding: 16px;
  }
  
  .test-content :deep(.ant-col) {
    margin-bottom: 16px;
  }
}
</style>
