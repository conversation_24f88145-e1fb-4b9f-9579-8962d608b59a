# 前端开发指南

## 文档信息
- **版本**: v1.3.0
- **最后更新**: 2025-07-29
- **维护者**: <PERSON> (工程师)
- **状态**: Sprint 02修复完成 - 文件上传功能已修复，TypeScript编译错误已解决

## 项目概述
期末复习平台前端应用，基于Vue3 + TypeScript + Vben Admin构建，提供现代化、响应式的用户界面和优秀的用户体验。

## 技术栈

### 核心框架
- **前端框架**: Vue 3.x (Composition API)
- **构建工具**: Vite 5.x
- **路由管理**: Vue Router 4.x
- **状态管理**: Pinia
- **UI组件库**: Ant Design Vue 4.x
- **样式方案**: UnoCSS (原子化CSS)
- **管理模板**: Vben Admin

### 开发工具
- **包管理器**: npm
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript 5.x
- **自动导入**: unplugin-auto-import + unplugin-vue-components
- **端到端测试**: Playwright (强制使用)
- **版本控制**: Git
- **HTTP客户端**: Axios

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口层
│   │   ├── index.ts       # Axios配置和拦截器
│   │   ├── subject.ts     # 学科相关API
│   │   └── file.ts        # 文件管理API
│   ├── components/        # 通用组件
│   │   ├── SubjectCard.vue      # 学科卡片组件
│   │   ├── CreateSubjectModal.vue # 创建学科模态框
│   │   ├── SubjectManager.vue   # 学科管理组件
│   │   ├── FileUploader.vue     # 文件上传组件
│   │   └── index.ts            # 组件导出
│   ├── router/           # 路由配置
│   │   └── index.ts      # 路由定义和守卫
│   ├── stores/           # Pinia状态管理
│   │   └── subject.ts    # 学科状态管理
│   ├── styles/           # 全局样式
│   │   └── index.css     # 全局CSS样式
│   ├── types/            # TypeScript类型定义
│   │   ├── index.ts      # 通用类型导出
│   │   ├── subject.ts    # 学科相关类型
│   │   └── file.ts       # 文件相关类型
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   │   ├── Home.vue      # 首页
│   │   ├── 404.vue       # 404页面
│   │   └── subjects/     # 学科管理页面
│   │       ├── index.vue # 学科列表
│   │       ├── create.vue# 创建学科
│   │       └── detail.vue# 学科详情
│   ├── App.vue           # 根组件
│   └── main.ts           # 应用入口
├── index.html            # HTML模板
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript配置
├── vite.config.ts        # Vite配置
├── uno.config.ts         # UnoCSS配置
├── .eslintrc.js          # ESLint配置
├── .prettierrc           # Prettier配置
├── auto-imports.d.ts     # 自动导入类型声明
└── .eslintrc-auto-import.json # ESLint自动导入配置
```

## 核心功能模块

### 学科管理模块 (已实现)
- 学科列表展示
- 学科创建功能
- 学科详情查看
- 学科信息编辑

### 复习材料模块 (待开发)
- 材料列表展示
- 材料详情查看
- 材料搜索过滤
- 材料收藏管理

### 学习进度模块 (待开发)
- 进度可视化
- 学习统计
- 学习计划
- 成就系统

### 用户中心模块 (待开发)
- 个人信息管理
- 学习偏好设置
- 通知设置
- 账户安全

## 开发规范

### 组件开发规范
- 使用Vue3 Composition API
- 组件名称使用PascalCase
- 文件名使用kebab-case
- 单文件组件(.vue)格式
- 使用`<script setup>`语法糖

#### 文件上传组件规范
基于FileUploader.vue组件的最佳实践：

**Props设计原则**:
```typescript
interface FileUploaderProps {
  subjectId: number      // 必需参数，明确业务关联
  accept?: string        // 可选参数，提供默认值
  maxSize?: number       // 可选参数，合理的默认限制
  disabled?: boolean     // 状态控制参数
}
```

**Events设计原则**:
```typescript
interface FileUploaderEmits {
  uploadStart: []                    // 无参数事件
  uploadProgress: [progress: number] // 单参数事件
  uploadSuccess: [file: FileNode]    // 复杂对象事件
  uploadError: [error: string]       // 错误信息事件
}
```

**状态管理模式**:
- 使用明确的状态枚举：`'idle' | 'uploading' | 'success' | 'error'`
- 状态转换清晰：idle → uploading → success/error
- 提供状态重置机制
- 错误状态可恢复

**用户体验要求**:
- 支持拖拽和点击两种交互方式
- **新增明确的"选择文件上传"按钮**，解决用户找不到上传入口的问题
- **优化上传流程**：选择文件 → 显示文件信息 → 确认上传 → 自动跳转
- **文件预览功能**：选择文件后显示文件名、大小等关键信息
- **双重确认机制**：提供"确认上传"和"取消"按钮，提升用户控制感
- 提供实时进度反馈和状态指示
- 友好的错误提示信息和恢复机制
- 响应式设计适配各种屏幕尺寸，移动端友好
- 与Ant Design Vue保持视觉一致性

**UI设计原则** (v0.9.3更新):
- **按钮层次化设计**：主要操作使用primary按钮，次要操作使用default按钮
- **视觉引导**：通过颜色、大小、图标引导用户操作流程
- **状态反馈**：每个操作都有明确的视觉反馈和状态提示
- **容错设计**：允许用户取消操作，提供重试机制

### 代码风格规范
- 使用TypeScript进行严格类型检查
- 遵循ESLint和Prettier配置
- 使用语义化的变量和函数命名
- 添加必要的代码注释
- 自动导入Vue API和组件

### 状态管理规范
- 全局状态使用Pinia
- 组件内部状态使用ref/reactive
- 异步状态使用专门的composable
- 避免过度使用全局状态
- 使用storeToRefs解构响应式数据

#### FileStore扩展功能 (v0.9.7新增)
**文件状态管理**：
- **文件列表状态**：fileList、currentSubjectId、currentParentId、breadcrumbItems
- **搜索状态**：searchResults、searchKeyword、isSearching、searchError
- **分页状态**：currentPage、pageSize、totalFiles、totalPages
- **计算属性**：hasFileList、isInSearchMode、isRootDirectory等

**使用示例**：
```typescript
import { useFileStore } from '@/stores'

const fileStore = useFileStore()

// 获取文件列表
await fileStore.getFileList(subjectId, { page: 1, limit: 20 })

// 搜索文件
await fileStore.searchFiles(subjectId, { q: '关键词', limit: 20 })

// 导航到目录
await fileStore.navigateToDirectory(subjectId, parentId)

// 状态判断
if (fileStore.hasFileList) {
  // 处理文件列表
}

// 清除状态
fileStore.clearFileListState()
fileStore.clearSearchState()
```

### API调用规范
- 统一的Axios服务封装
- 错误处理和重试机制
- 请求和响应拦截器
- 加载状态管理
- 统一的API响应格式

## 前后端数据联调

### HTTP客户端配置

项目使用axios作为HTTP客户端，配置文件位于`src/api/index.ts`：

```typescript
import axios, { AxiosInstance } from 'axios'

const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    config.headers['X-Request-ID'] = Date.now().toString()
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data)
    return config
  },
  (error) => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    return response
  },
  (error) => {
    console.error('[API Response Error]', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export default api
```

### API服务层实现

#### 学科管理API
学科管理API服务位于`src/api/subject.ts`，提供完整的CRUD操作：

```typescript
import api from './index'
import type { Subject, CreateSubjectDto, SubjectListResponse, CreateSubjectResponse, SubjectDetailResponse } from '@/types/subject'

export const subjectApi = {
  // 获取学科列表
  async getSubjects() {
    return api.get<SubjectListResponse>('/subjects')
  },

  // 创建学科
  async createSubject(data: CreateSubjectDto) {
    return api.post<CreateSubjectResponse>('/subjects', data)
  },

  // 获取学科详情
  async getSubjectById(id: number) {
    return api.get<SubjectDetailResponse>(`/subjects/${id}`)
  }
}
```

#### 文件管理API
文件管理API服务位于`src/api/file.ts`，提供文件上传、获取、浏览、搜索功能：

```typescript
import api from './index'
import type {
  ApiResponse,
  FileNode,
  UploadFileResponse,
  FileContentResponse,
  FileListResponse,
  BreadcrumbResponse,
  FileSearchResponse,
  FileListParams,
  FileSearchParams
} from '@/types'

export const fileApi = {
  // 上传文件到指定学科
  async uploadFile(subjectId: number, file: File): Promise<ApiResponse<UploadFileResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    return api.post(`/subjects/${subjectId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 根据文件ID获取文件内容
  async getFileContent(fileId: number): Promise<ApiResponse<FileContentResponse>> {
    return api.get(`/files/${fileId}/content`)
  },

  // 根据文件ID获取文件信息
  async getFileInfo(fileId: number): Promise<ApiResponse<FileNode>> {
    return api.get(`/files/${fileId}`)
  },

  // Sprint 03 新增API - 获取学科文件列表 (支持分页、过滤、层级浏览)
  async getFileList(subjectId: number, params?: FileListParams): Promise<ApiResponse<FileListResponse>> {
    const searchParams = new URLSearchParams()

    if (params?.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params?.type) {
      searchParams.append('type', params.type)
    }
    if (params?.parentId) {
      searchParams.append('parentId', params.parentId.toString())
    }
    if (params?.search) {
      searchParams.append('search', params.search)
    }

    const queryString = searchParams.toString()
    const url = queryString ? `/subjects/${subjectId}/files?${queryString}` : `/subjects/${subjectId}/files`

    return api.get(url)
  },

  // Sprint 03 新增API - 获取面包屑导航
  async getBreadcrumb(fileId: number): Promise<ApiResponse<BreadcrumbResponse>> {
    return api.get(`/files/${fileId}/breadcrumb`)
  },

  // Sprint 03 新增API - 文件搜索
  async searchFiles(subjectId: number, params: FileSearchParams): Promise<ApiResponse<FileSearchResponse>> {
    const searchParams = new URLSearchParams()

    // 搜索关键词是必需的
    searchParams.append('q', params.q)

    if (params.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params.type) {
      searchParams.append('type', params.type)
    }

    return api.get(`/subjects/${subjectId}/search?${searchParams.toString()}`)
  }
}
}
```

**文件上传最佳实践**:
- 使用FormData格式传输文件
- 设置正确的Content-Type头部
- 实现上传进度监控
- 处理文件大小和类型验证
- 提供友好的错误处理

#### Sprint 03 新API使用示例

**1. 文件浏览组件示例**
```typescript
// FileExplorer.vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { fileApi } from '@/api/file'

const subjectId = ref(1)
const files = ref([])
const pagination = ref({})
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const fileType = ref<'file' | 'folder' | undefined>()
const searchKeyword = ref('')

// 获取文件列表
const loadFiles = async (parentId?: number) => {
  loading.value = true
  try {
    const response = await fileApi.getSubjectFiles(subjectId.value, {
      page: currentPage.value,
      limit: pageSize.value,
      type: fileType.value,
      parentId,
      search: searchKeyword.value || undefined
    })
    files.value = response.data.files
    pagination.value = response.data.pagination
  } catch (error) {
    console.error('加载文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索文件
const searchFiles = async () => {
  if (!searchKeyword.value || searchKeyword.value.length < 2) return

  loading.value = true
  try {
    const response = await fileApi.searchFiles(subjectId.value, searchKeyword.value, {
      limit: 20,
      type: fileType.value
    })
    files.value = response.data.results
  } catch (error) {
    console.error('搜索文件失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => loadFiles())
</script>
```

**2. 面包屑导航组件示例**
```typescript
// BreadcrumbNav.vue
<script setup lang="ts">
import { ref, watch } from 'vue'
import { fileApi } from '@/api/file'

const props = defineProps<{
  currentFileId?: number
}>()

const breadcrumb = ref([])
const loading = ref(false)

// 获取面包屑路径
const loadBreadcrumb = async (fileId: number) => {
  loading.value = true
  try {
    const response = await fileApi.getBreadcrumb(fileId)
    breadcrumb.value = response.data.breadcrumb
  } catch (error) {
    console.error('获取面包屑失败:', error)
  } finally {
    loading.value = false
  }
}

watch(() => props.currentFileId, (newFileId) => {
  if (newFileId) {
    loadBreadcrumb(newFileId)
  }
}, { immediate: true })
</script>

<template>
  <nav class="breadcrumb" v-if="breadcrumb.length > 0">
    <span
      v-for="(item, index) in breadcrumb"
      :key="item.id || 'subject'"
      class="breadcrumb-item"
    >
      <router-link
        v-if="item.type === 'subject'"
        :to="`/subjects/${item.id}`"
      >
        {{ item.name }}
      </router-link>
      <router-link
        v-else-if="item.type === 'folder'"
        :to="`/files/${item.id}`"
      >
        {{ item.name }}
      </router-link>
      <span v-else>{{ item.name }}</span>
      <span v-if="index < breadcrumb.length - 1" class="separator">/</span>
    </span>
  </nav>
</template>
```

**3. 智能搜索组件示例**
```typescript
// SmartSearch.vue
<script setup lang="ts">
import { ref, computed } from 'vue'
import { fileApi } from '@/api/file'
import { debounce } from 'lodash-es'

const props = defineProps<{
  subjectId: number
}>()

const searchKeyword = ref('')
const searchResults = ref([])
const isSearching = ref(false)
const showResults = ref(false)

// 防抖搜索
const debouncedSearch = debounce(async (keyword: string) => {
  if (keyword.length < 2) {
    searchResults.value = []
    showResults.value = false
    return
  }

  isSearching.value = true
  try {
    const response = await fileApi.searchFiles(props.subjectId, keyword, {
      limit: 10
    })
    searchResults.value = response.data.results
    showResults.value = true
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    isSearching.value = false
  }
}, 300)

// 监听搜索关键词变化
watch(searchKeyword, (newKeyword) => {
  debouncedSearch(newKeyword)
})

// 高亮搜索结果
const highlightedResults = computed(() => {
  return searchResults.value.map(result => ({
    ...result,
    displayName: result.highlight?.highlighted || result.name
  }))
})
</script>
```

### 状态管理集成

使用Pinia进行状态管理，学科状态管理位于`src/stores/subject.ts`：

```typescript
import { defineStore } from 'pinia'
import { subjectApi } from '@/api/subject'
import type { Subject } from '@/types/subject'

export const useSubjectStore = defineStore('subject', () => {
  const subjects = ref<Subject[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取学科列表
  const fetchSubjects = async () => {
    setLoading(true)
    clearError()
    try {
      const response = await subjectApi.getSubjects()
      subjects.value = response.data.data
    } catch (error: any) {
      setError(error.message || '获取学科列表失败')
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 创建学科
  const createSubject = async (data: CreateSubjectDto) => {
    try {
      const response = await subjectApi.createSubject(data)
      const newSubject = response.data.data
      subjects.value.unshift(newSubject) // 添加到列表开头
      return newSubject
    } catch (error: any) {
      setError(error.message || '创建学科失败')
      throw error
    }
  }

  return {
    subjects: readonly(subjects),
    loading: readonly(loading),
    error: readonly(error),
    fetchSubjects,
    createSubject
  }
})
```

**文件状态管理 (v0.9.7新增)**：

```typescript
import { defineStore } from 'pinia'
import { fileApi } from '@/api/file'
import type { FileNode, SearchResultItem, BreadcrumbItem } from '@/types/file'

export const useFileStore = defineStore('file', () => {
  // 文件浏览状态
  const fileList = ref<FileNode[]>([])
  const currentSubjectId = ref<number | null>(null)
  const currentParentId = ref<number | null>(null)
  const breadcrumbItems = ref<BreadcrumbItem[]>([])

  // 搜索状态
  const searchResults = ref<SearchResultItem[]>([])
  const searchKeyword = ref<string>('')
  const isSearching = ref(false)
  const searchError = ref<string | null>(null)

  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalFiles = ref(0)
  const totalPages = ref(0)

  // 计算属性
  const hasFileList = computed(() => fileList.value.length > 0)
  const hasSearchResults = computed(() => searchResults.value.length > 0)
  const isInSearchMode = computed(() => !!searchKeyword.value)
  const isRootDirectory = computed(() => currentParentId.value === null)

  // 获取文件列表
  const getFileList = async (subjectId: number, params?: FileListParams) => {
    isFileListLoading.value = true
    clearFileListError()

    try {
      const response = await fileApi.getFileList(subjectId, params)
      const data = response.data

      fileList.value = data.files
      currentSubjectId.value = data.subjectId
      currentParentId.value = data.parentId
      totalFiles.value = data.pagination.total
      totalPages.value = data.pagination.totalPages
      currentPage.value = data.pagination.page

      return data
    } catch (error: any) {
      setFileListError(error.message || '获取文件列表失败')
      throw error
    } finally {
      isFileListLoading.value = false
    }
  }

  // 搜索文件
  const searchFiles = async (subjectId: number, params: FileSearchParams) => {
    isSearching.value = true
    clearSearchError()

    try {
      const response = await fileApi.searchFiles(subjectId, params)
      const data = response.data

      searchResults.value = data.results
      searchKeyword.value = data.keyword

      return data
    } catch (error: any) {
      setSearchError(error.message || '搜索文件失败')
      throw error
    } finally {
      isSearching.value = false
    }
  }

  return {
    // 状态
    fileList: readonly(fileList),
    searchResults: readonly(searchResults),
    currentSubjectId: readonly(currentSubjectId),
    // 计算属性
    hasFileList,
    isInSearchMode,
    isRootDirectory,
    // 方法
    getFileList,
    searchFiles,
    clearFileListState,
    clearSearchState
  }
})
```

### 组件API集成

组件中集成API调用的最佳实践：

**1. CreateSubjectModal.vue - 创建学科模态框**

```typescript
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    const createData: CreateSubjectDto = {
      name: formData.value.name.trim(),
      description: formData.value.description?.trim() || ''
    }

    const response = await subjectApi.createSubject(createData)
    const newSubject = response.data.data

    message.success('学科创建成功！')
    emit('success', newSubject)
    resetForm()
    visible.value = false
  } catch (error: any) {
    console.error('创建学科失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '创建学科失败，请重试'
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}
```

**2. SubjectManager.vue - 学科管理容器**

```typescript
const loadSubjects = async () => {
  loading.value = true
  try {
    const response = await subjectApi.getSubjects()
    subjects.value = response.data.data
  } catch (error: any) {
    console.error('加载学科列表失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '加载学科列表失败'
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}
```

### 错误处理和用户反馈

**统一错误处理策略：**

1. **API层错误处理**：在axios拦截器中统一处理HTTP错误
2. **组件层错误处理**：在具体操作中捕获并显示用户友好的错误信息
3. **状态管理错误处理**：在store中维护错误状态

**用户反馈机制：**

1. **成功提示**：使用`message.success()`显示操作成功信息
2. **错误提示**：使用`message.error()`显示错误信息
3. **加载状态**：使用loading状态显示操作进度
4. **表单验证**：实时验证用户输入并提供反馈

### 数据流完整性验证

前后端数据联调的完整流程：

1. **用户操作** → 触发组件事件
2. **表单验证** → 验证用户输入数据
3. **API调用** → 发送HTTP请求到后端
4. **数据传输** → 请求/响应数据格式化
5. **状态更新** → 更新前端状态管理
6. **UI更新** → 重新渲染用户界面
7. **用户反馈** → 显示操作结果

**验证要点：**
- API响应格式与TypeScript类型定义一致
- 错误处理覆盖网络错误、服务器错误、业务逻辑错误
- 状态管理与UI组件数据同步
- 用户操作有及时的视觉反馈

**实际验证结果：**
- ✅ 成功创建测试学科"前后端数据联调测试学科"
- ✅ 数据持久化验证：页面刷新后数据仍然存在
- ✅ 用户反馈机制：显示"学科创建成功！"提示
- ✅ 状态同步：学科总数从104增加到105
- ✅ UI更新：新学科显示为列表中的#105项

## 样式开发指南

### 设计系统
- 基于Ant Design Vue的设计规范
- UnoCSS原子化CSS类
- 统一的颜色规范
- 标准化的字体大小和间距

### CSS规范
- 优先使用UnoCSS原子类
- 组件内使用scoped样式
- 避免全局样式污染
- 响应式设计支持
- 支持深色模式切换

## 性能优化

### 后端数据库优化 (v0.9.4)
**数据库索引优化对前端的影响**:
- ✅ **文件浏览响应速度大幅提升**: 后端查询时间从未优化状态优化至平均2.20ms
- ✅ **用户体验显著改善**: 文件列表加载、搜索、导航切换都将获得近乎即时的响应
- ✅ **为Sprint 03做好准备**: 文件浏览功能的性能基础已就绪，前端可专注于UI/UX实现
- ✅ **API调用优化**: 所有文件相关API调用都将受益于数据库索引优化

**前端开发建议**:
- 文件浏览组件可以设计更丰富的交互，因为后端响应足够快
- 可以实现实时搜索功能，无需担心频繁API调用的性能问题
- 分页加载可以使用较小的页面大小，提供更流畅的滚动体验

### 代码分割
- 路由级别的代码分割
- 组件懒加载
- 第三方库按需引入
- 动态导入优化

### 渲染优化
- 使用v-memo优化重渲染
- 合理使用computed和watchEffect
- 虚拟滚动处理大列表
- 图片懒加载和组件懒加载

### 缓存策略
- HTTP缓存配置
- Service Worker缓存
- 本地存储优化
- API响应缓存

## 测试策略

### 单元测试
- 组件测试覆盖率 > 80%
- 工具函数100%覆盖
- 使用Vitest + Vue Test Utils
- Mock外部依赖

### 集成测试
- 页面级别的集成测试
- API集成测试
- Pinia状态管理测试
- Vue Router路由测试

### 端到端测试 (Playwright - 强制使用)
- 关键用户流程测试
- 跨浏览器兼容性测试
- 移动端适配测试
- 性能测试

## 部署与构建

### 构建配置
- 生产环境优化
- 资源压缩和合并
- 环境变量配置
- 构建产物分析

### 部署流程
- 自动化部署流程
- 多环境部署策略
- 回滚机制
- 监控和告警

## 开发工作流

### Git工作流
- Feature分支开发
- Pull Request代码审查
- 自动化测试检查
- 合并前的质量门禁

### 开发环境
- 本地开发服务器
- 热重载配置
- 代理API配置
- 开发工具集成

## 常见问题与解决方案

### 性能问题
- 组件重渲染优化
- 内存泄漏排查
- 包体积优化
- 首屏加载优化

### 兼容性问题
- 浏览器兼容性处理
- 移动端适配
- 第三方库兼容
- Polyfill配置

## 开发环境配置

### 本地开发
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器 (端口3000)
npm run dev

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 环境变量
- `VITE_API_BASE_URL`: API基础URL (默认: http://localhost:3001)
- `VITE_APP_TITLE`: 应用标题

### 代理配置
开发环境下，前端(3000端口)通过Vite代理转发API请求到后端(3001端口)

```typescript
// vite.config.ts
server: {
  port: 3000,
  host: true,
  proxy: {
    '/api': {
      target: 'http://localhost:3001',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

## 已实现功能

### 基础框架 ✅
- Vue3 + TypeScript + Vben Admin基础框架
- Vite构建配置和开发服务器
- UnoCSS原子化CSS框架
- Ant Design Vue UI组件库
- 自动导入配置(API和组件)

### 路由系统 ✅
- Vue Router 4配置
- 路由守卫和页面标题设置
- 404错误页面处理

### 状态管理 ✅
- Pinia状态管理配置
- 学科管理store实现
- 响应式数据处理

### API服务 ✅
- Axios HTTP客户端配置
- 请求/响应拦截器
- 统一错误处理
- 学科管理API接口

### 页面组件 ✅
- 首页展示和导航
- 学科列表页面
- 学科创建页面
- 学科详情页面

### 学科管理组件体系 ✅
- **SubjectCard.vue**: 学科卡片组件，支持响应式布局和交互操作
- **CreateSubjectModal.vue**: 创建学科弹窗，包含表单验证和实时预览
- **SubjectManager.vue**: 学科管理容器，提供搜索和视图切换功能
- **SubjectList.vue**: 学科列表页面，集成所有管理功能

### 文件管理组件体系 ✅
- **FileBrowser.vue**: 文件浏览组件，支持层级导航、搜索、虚拟滚动、多视图模式
- **BreadcrumbNav.vue**: 面包屑导航组件，支持路径显示、快速跳转、响应式布局、智能省略
- **FileSearch.vue**: 文件搜索组件，支持实时搜索、搜索历史、高级过滤、结果高亮
- **FileUploader.vue**: 文件上传组件，支持拖拽上传和进度显示
- **MarkdownViewer.vue**: Markdown文件预览组件，支持语法高亮

## 学科管理组件开发详解

### 组件架构设计
学科管理采用组件化架构，将功能拆分为独立、可复用的组件：

```
SubjectList (页面级)
└── SubjectManager (容器组件)
    ├── CreateSubjectModal (弹窗组件)
    └── SubjectCard[] (卡片组件数组)
```

### 核心组件实现

#### 1. SubjectCard 组件
**功能特性**:
- 响应式设计：桌面多列网格、平板双列、移动端单列
- 悬停效果和操作按钮
- 事件处理：点击、编辑、删除
- TypeScript类型安全

**关键代码**:
```vue
<template>
  <div class="subject-card bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4">
    <div class="flex justify-between items-start mb-2">
      <h3 class="text-lg font-semibold text-gray-900 cursor-pointer" @click="handleClick">
        {{ subject.name }}
      </h3>
      <span class="text-sm text-gray-500">#{{ subject.id }}</span>
    </div>
    <!-- 更多内容... -->
  </div>
</template>

<script setup lang="ts">
interface Props {
  subject: Subject
}
const emit = defineEmits<{
  click: [subject: Subject]
  edit: [subject: Subject]
  delete: [subject: Subject]
}>()
</script>
```

#### 2. CreateSubjectModal 组件
**功能特性**:
- 表单验证和字符计数
- 实时预览功能
- API集成和错误处理
- 加载状态管理

**数据持久化解决方案**:
组件已修复数据持久化问题，确保创建的学科在页面刷新后仍然存在：
```typescript
// 修复前：使用模拟数据
await new Promise(resolve => setTimeout(resolve, 1000))

// 修复后：使用真实API
const response = await subjectApi.createSubject(createData)
const newSubject = response.data.data
```

#### 3. SubjectManager 组件
**功能特性**:
- 搜索功能实现
- 网格/列表视图切换
- 空状态处理
- 数据加载和状态管理

**API集成修复**:
```typescript
// 修复前：使用硬编码数据
subjects.value = [
  { id: 1, name: '示例学科1', description: '示例描述1' },
  { id: 2, name: '示例学科2', description: '示例描述2' }
]

// 修复后：调用真实API
const response = await subjectApi.getSubjects()
subjects.value = response.data.data
```

### 响应式设计实现
使用UnoCSS实现响应式布局：
```css
/* 桌面端：多列网格 */
.grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4

/* 平板端：双列布局 */
md:grid-cols-2

/* 移动端：单列布局 */
grid-cols-1
```

### TypeScript类型定义
完整的类型系统确保代码质量：
```typescript
// 基础类型
export interface Subject {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
}

// 组件Props类型
export interface SubjectCardProps {
  subject: Subject
}

// 组件Emits类型
export interface SubjectCardEmits {
  click: [subject: Subject]
  edit: [subject: Subject]
  delete: [subject: Subject]
}
```

### 性能优化措施
1. **组件懒加载**: 使用动态导入减少初始包体积
2. **事件防抖**: 搜索功能使用防抖优化性能
3. **虚拟滚动**: 大量数据时使用虚拟滚动
4. **缓存策略**: API响应缓存减少重复请求

## 文件管理组件开发详解

### 组件架构设计
文件管理采用模块化架构，将功能拆分为专门的组件：

```
SubjectDetail (页面级)
└── FileBrowser (文件浏览容器)
    ├── BreadcrumbNav (面包屑导航)
    ├── FileSearch (文件搜索)
    └── FileList (文件列表)
```

### 核心组件实现

#### 1. BreadcrumbNav 组件 ✅
**功能特性**:
- 智能路径显示：长路径自动省略，保留首末项
- 响应式设计：桌面端完整导航，移动端简化模式
- 主题支持：浅色/深色主题切换
- 配置灵活：支持自定义分隔符、尺寸、功能开关

**关键代码**:
```vue
<template>
  <div v-if="breadcrumbItems.length > 0" class="breadcrumb-nav" :class="containerClass">
    <!-- 桌面端完整面包屑 -->
    <div v-if="!isMobile" class="desktop-breadcrumb">
      <a-breadcrumb :separator="separator">
        <a-breadcrumb-item
          v-for="(item, index) in visibleItems"
          :key="item.id || `item-${index}`"
          @click="handleItemClick(item, index)"
        >
          <i :class="getItemIcon(item.type)" class="item-icon"></i>
          <span class="item-text">{{ item.name }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 移动端简化面包屑 -->
    <div v-else class="mobile-breadcrumb">
      <div class="back-button" @click="handleBackClick">
        <i class="i-carbon-arrow-left"></i>
        <span>返回</span>
      </div>
      <div class="current-location">
        <i :class="getItemIcon(currentItem.type)" class="location-icon"></i>
        <span class="location-text">{{ currentItem.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { BreadcrumbItem } from '@/types'

interface Props {
  items: BreadcrumbItem[]
  maxItems?: number
  separator?: string
  theme?: 'light' | 'dark'
  responsive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 5,
  separator: '/',
  theme: 'light',
  responsive: true
})

interface Emits {
  itemClick: [item: BreadcrumbItem, index: number]
  backClick: [previousItem: BreadcrumbItem]
  pathChange: [newPath: BreadcrumbItem[]]
}

const emit = defineEmits<Emits>()
</script>
```

**智能省略算法**:
```typescript
const visibleItems = computed(() => {
  if (!props.responsive || expanded.value || breadcrumbItems.value.length <= props.maxItems) {
    return breadcrumbItems.value
  }

  const items = breadcrumbItems.value
  if (items.length <= 3) return items

  return [
    items[0], // 首项
    ...items.slice(-2) // 最后两项
  ]
})

const hiddenItems = computed(() => {
  const items = breadcrumbItems.value
  if (items.length <= 3) return []

  return items.slice(1, -2) // 中间被隐藏的项
})
```

**响应式检测**:
```typescript
const checkMobile = () => {
  if (!props.responsive) return
  isMobile.value = window.innerWidth < 768
}

onMounted(() => {
  if (props.responsive) {
    checkMobile()
    window.addEventListener('resize', checkMobile)
  }
})
```

#### 2. BreadcrumbNav 使用指南

**基础使用**:
```vue
<template>
  <BreadcrumbNav
    :items="breadcrumbItems"
    @item-click="handleItemClick"
    @path-change="handlePathChange"
  />
</template>

<script setup lang="ts">
import { BreadcrumbNav } from '@/components'
import type { BreadcrumbItem } from '@/types'

const breadcrumbItems = ref<BreadcrumbItem[]>([
  { id: null, name: '期末复习平台', type: 'subject', level: 0 },
  { id: 1, name: '数学', type: 'folder', level: 1 },
  { id: 2, name: '高等数学', type: 'folder', level: 2 }
])

const handleItemClick = (item: BreadcrumbItem, index: number) => {
  // 处理面包屑点击导航
  console.log('导航到:', item.name)
}

const handlePathChange = (newPath: BreadcrumbItem[]) => {
  // 处理路径变化
  breadcrumbItems.value = newPath
}
</script>
```

**高级配置**:
```vue
<template>
  <BreadcrumbNav
    :items="breadcrumbItems"
    :max-items="5"
    separator=">"
    theme="dark"
    size="large"
    :show-icons="true"
    :clickable="true"
    :responsive="true"
    @item-click="handleItemClick"
    @back-click="handleBackClick"
    @path-change="handlePathChange"
  />
</template>
```

**与FileBrowser集成**:
```vue
<template>
  <div class="file-management">
    <BreadcrumbNav
      :items="currentPath"
      @item-click="navigateToPath"
    />
    <FileBrowser
      :subject-id="subjectId"
      :initial-parent-id="currentFolderId"
      @folder-enter="handleFolderEnter"
    />
  </div>
</template>

<script setup lang="ts">
const currentPath = ref<BreadcrumbItem[]>([])
const currentFolderId = ref<number | null>(null)

const navigateToPath = (item: BreadcrumbItem, index: number) => {
  // 更新当前路径
  currentPath.value = currentPath.value.slice(0, index + 1)
  currentFolderId.value = item.type === 'subject' ? null : item.id
}

const handleFolderEnter = (folder: FileNode) => {
  // 添加到面包屑路径
  currentPath.value.push({
    id: folder.id,
    name: folder.name,
    type: 'folder',
    level: currentPath.value.length
  })
  currentFolderId.value = folder.id
}
</script>
```

#### 2. FileBrowser 组件 ✅
**功能特性**:
- **多视图模式**: 支持列表视图和网格视图切换
- **虚拟滚动**: 处理大量文件时的性能优化，支持动态高度
- **层级导航**: 支持文件夹双击进入和面包屑导航
- **文件搜索**: 集成实时搜索功能，支持关键词高亮
- **类型过滤**: 支持按文件类型（文件/文件夹）过滤
- **响应式设计**: 桌面端多列网格，移动端单列列表

**关键代码**:
```vue
<template>
  <div class="file-browser">
    <!-- 工具栏 -->
    <div class="file-browser-toolbar">
      <FileSearch
        :subject-id="subjectId"
        @search="handleSearch"
        @result-click="handleSearchResultClick"
      />
      <div class="toolbar-actions">
        <a-select v-model:value="fileTypeFilter" placeholder="文件类型">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="file">文件</a-select-option>
          <a-select-option value="folder">文件夹</a-select-option>
        </a-select>
        <div class="view-toggle">
          <a-button-group>
            <a-button :type="viewMode === 'list' ? 'primary' : 'default'" @click="setViewMode('list')">
              <i class="i-carbon-list"></i>
            </a-button>
            <a-button :type="viewMode === 'grid' ? 'primary' : 'default'" @click="setViewMode('grid')">
              <i class="i-carbon-grid"></i>
            </a-button>
          </a-button-group>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <BreadcrumbNav
      v-if="showBreadcrumb"
      :items="breadcrumbItems"
      @item-click="handleBreadcrumbClick"
    />

    <!-- 文件列表 -->
    <div class="file-browser-content" :style="{ height: computedHeight }">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="file-grid">
        <VirtualList
          v-if="enableVirtualScroll && displayFiles.length > virtualScrollVisibleCount"
          :items="displayFiles"
          :item-height="virtualScrollItemHeight"
          :visible-count="virtualScrollVisibleCount"
          @scroll="handleVirtualScroll"
        >
          <template #item="{ item }">
            <FileItem
              :file="item"
              :view-mode="viewMode"
              @click="handleFileClick"
              @double-click="handleFileDoubleClick"
            />
          </template>
        </VirtualList>
        <div v-else class="file-grid-container">
          <FileItem
            v-for="file in displayFiles"
            :key="file.id"
            :file="file"
            :view-mode="viewMode"
            @click="handleFileClick"
            @double-click="handleFileDoubleClick"
          />
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="file-list">
        <a-table
          :columns="tableColumns"
          :data-source="displayFiles"
          :pagination="paginationConfig"
          :loading="loading"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="file-name-cell" @click="handleFileClick(record)" @dblclick="handleFileDoubleClick(record)">
                <i :class="getFileIcon(record)" class="file-icon"></i>
                <span class="file-name">{{ record.name }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'size'">
              {{ record.type === 'file' ? formatFileSize(record.file_size) : '-' }}
            </template>
            <template v-else-if="column.key === 'updated_at'">
              {{ formatDate(record.updated_at) }}
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { fileApi } from '@/api/file'
import { useVirtualScroll } from '@/composables/useVirtualScroll'
import type { FileNode, FileListParams, BreadcrumbItem } from '@/types'

interface Props {
  subjectId: number
  initialParentId?: number | null
  height?: string | number
  showBreadcrumb?: boolean
  enableSearch?: boolean
  enableVirtualScroll?: boolean
  virtualScrollItemHeight?: number
  virtualScrollVisibleCount?: number
}

interface Emits {
  fileClick: [file: FileNode]
  fileDoubleClick: [file: FileNode]
  folderEnter: [folder: FileNode]
  breadcrumbClick: [item: BreadcrumbItem, index: number]
}

const emit = defineEmits<Emits>()
</script>
```

**虚拟滚动集成**:
```typescript
// 使用useVirtualScroll组合式函数
const {
  containerRef,
  listRef,
  visibleItems,
  scrollTop,
  handleScroll
} = useVirtualScroll({
  items: displayFiles,
  itemHeight: props.virtualScrollItemHeight,
  visibleCount: props.virtualScrollVisibleCount,
  buffer: 5
})

// 虚拟滚动性能优化
const handleVirtualScroll = (event: Event) => {
  handleScroll(event)

  // 预加载逻辑
  const target = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = target

  if (scrollHeight - scrollTop - clientHeight < 100) {
    loadMoreFiles()
  }
}
```

#### 3. FileSearch 组件 ✅
**功能特性**:
- **实时搜索**: 支持防抖搜索，避免频繁API调用
- **搜索历史**: 自动保存搜索历史，支持快速重复搜索
- **结果高亮**: 搜索结果中关键词高亮显示
- **高级过滤**: 支持按文件类型、修改时间等条件过滤
- **搜索建议**: 基于历史记录提供搜索建议

**关键代码**:
```vue
<template>
  <div class="file-search">
    <div class="search-input-container">
      <a-input
        v-model:value="searchKeyword"
        :placeholder="placeholder"
        :disabled="disabled"
        class="search-input"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown.enter="handleEnterSearch"
      >
        <template #prefix>
          <i class="i-carbon-search search-icon"></i>
        </template>
        <template #suffix>
          <a-spin v-if="isSearching" size="small" />
          <i
            v-else-if="searchKeyword"
            class="i-carbon-close clear-icon"
            @click="clearSearch"
          ></i>
        </template>
      </a-input>

      <!-- 搜索历史下拉 -->
      <div v-if="showHistoryDropdown && searchHistory.length > 0" class="search-history-dropdown">
        <div class="history-header">
          <span>搜索历史</span>
          <a-button type="text" size="small" @click="clearHistory">清空</a-button>
        </div>
        <div class="history-list">
          <div
            v-for="item in searchHistory"
            :key="item"
            class="history-item"
            @click="selectHistoryItem(item)"
          >
            <i class="i-carbon-time history-icon"></i>
            <span class="history-text">{{ item }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="showResults && searchResults.length > 0" class="search-results">
      <div class="results-header">
        <span class="results-count">找到 {{ totalResults }} 个结果</span>
        <a-button v-if="hasMoreResults" type="link" size="small" @click="loadMoreResults">
          加载更多
        </a-button>
      </div>
      <div class="results-list">
        <div
          v-for="result in searchResults"
          :key="result.id"
          class="result-item"
          @click="handleResultClick(result)"
        >
          <i :class="getFileIcon(result)" class="result-icon"></i>
          <div class="result-content">
            <div class="result-name" v-html="result.highlight?.name || result.name"></div>
            <div class="result-path">{{ result.path }}</div>
            <div class="result-meta">
              <span class="result-size">{{ formatFileSize(result.file_size) }}</span>
              <span class="result-date">{{ formatDate(result.updated_at) }}</span>
              <a-tag :color="getScoreColor(result.score)" size="small">
                匹配度: {{ result.score }}%
              </a-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { debounce } from 'lodash-es'
import { fileApi } from '@/api/file'
import type { FileSearchParams, SearchResultItem } from '@/types'

// 防抖搜索函数
const debouncedSearch = debounce(async (keyword: string) => {
  if (!keyword.trim() || keyword.length < 2) {
    searchResults.value = []
    totalResults.value = 0
    return
  }

  isSearching.value = true
  try {
    const params: FileSearchParams = {
      q: keyword.trim(),
      limit: props.resultLimit
    }

    const response = await fileApi.searchFiles(props.subjectId, params)
    const data = response.data.data

    searchResults.value = data.results
    totalResults.value = data.total
    currentPage.value = 1

    // 保存搜索历史
    saveToHistory(keyword.trim())

    // 触发搜索事件
    emit('search', keyword.trim(), data.results)
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
    totalResults.value = 0
  } finally {
    isSearching.value = false
  }
}, props.debounceDelay)
</script>
```

#### 4. 开发最佳实践

**性能优化**:
1. **计算属性缓存**: 使用computed缓存复杂计算结果
2. **事件监听管理**: 正确绑定和清理resize事件监听器
3. **条件渲染**: 根据配置动态渲染组件部分
4. **防抖处理**: 对频繁触发的事件进行防抖处理
5. **虚拟滚动**: 大量数据时使用虚拟滚动优化渲染性能

**响应式设计**:
1. **断点设计**: 768px作为桌面/移动端切换断点
2. **触摸优化**: 移动端按钮尺寸适配触摸操作
3. **空间利用**: 移动端紧凑布局，桌面端宽松布局
4. **交互模式**: 桌面端完整功能，移动端简化交互

**类型安全**:
```typescript
// 完整的类型定义
export interface BreadcrumbItem {
  id: number | null
  name: string
  type: 'subject' | 'folder' | 'file'
  level: number
}

export interface FileNode {
  id: number
  subject_id: number
  parent_id: number | null
  name: string
  type: 'file' | 'folder'
  file_path: string | null
  file_size: number | null
  mime_type: string | null
  created_at: string
  updated_at: string
}

export interface SearchResultItem extends FileNode {
  score: number
  highlight?: {
    name?: string
    content?: string
  }
  path: string
}

export interface FileBrowserProps {
  subjectId: number
  initialParentId?: number | null
  height?: string | number
  showBreadcrumb?: boolean
  enableSearch?: boolean
  enableVirtualScroll?: boolean
  virtualScrollItemHeight?: number
  virtualScrollVisibleCount?: number
}

export interface FileBrowserEmits {
  fileClick: [file: FileNode]
  fileDoubleClick: [file: FileNode]
  folderEnter: [folder: FileNode]
  breadcrumbClick: [item: BreadcrumbItem, index: number]
}
```

### 测试覆盖
- **单元测试**: 组件逻辑和交互测试
- **集成测试**: 组件间协作测试
- **E2E测试**: 完整用户流程测试（使用Playwright）

#### 端到端测试详情
**测试文件位置**: `frontend/tests/e2e/`

**测试覆盖范围**:
1. **完整用户故事测试**
   - 访问首页 → 导航到学科管理 → 创建学科 → 验证列表更新
   - 数据持久化验证（页面刷新测试）
   - 搜索功能测试
   - 视图切换功能测试

2. **边界条件和异常场景测试**
   - 创建重复名称学科测试
   - 创建空名称学科测试
   - 超长内容测试
   - 网络错误处理测试
   - 弹窗取消和关闭测试

3. **性能测试**
   - 页面加载性能测试（< 3秒）
   - API响应性能测试（< 2秒）
   - 大量数据渲染性能测试

4. **兼容性测试**
   - 多设备兼容性测试（桌面、平板、移动）
   - 多浏览器兼容性测试（Chrome、Firefox、Safari、Edge）
   - 键盘导航兼容性测试
   - 无障碍访问兼容性测试

**测试运行命令**:
```bash
# 运行所有端到端测试
npm run test:e2e

# 显示浏览器窗口运行测试
npm run test:e2e:headed

# 调试模式运行测试
npm run test:e2e:debug

# 只运行完整用户故事测试
npm run test:e2e:story

# 查看测试报告
npm run test:e2e:report
```

## 更新日志
- 2025-07-29: 后端数据库性能优化完成，为前端文件浏览功能提供高性能支撑
  - **性能基础优化**: 后端数据库索引优化完成，文件查询平均响应时间2.20ms
  - **开发指导更新**: 添加数据库优化对前端开发的影响说明和建议
  - **Sprint 03准备**: 为即将开发的文件浏览功能奠定了坚实的性能基础
- 2025-01-28: 完成Vue3 + TypeScript + Vben Admin基础框架搭建
- 2025-01-28: 实现学科管理功能和前后端数据联调
- 2025-01-28: 创建前端开发指南模板
- 2025-01-28: 完成学科管理组件体系开发，解决数据持久化问题
- 2025-01-28: 完成端到端测试开发，包含完整用户故事测试、边界条件测试、性能测试和兼容性测试

---
**注意**: 此指南将随着项目开发进展持续更新和完善。