<template>
  <div class="file-search">
    <!-- 搜索输入框 -->
    <div class="search-input-container">
      <a-input-search
        v-model:value="searchKeyword"
        :placeholder="placeholder"
        :loading="isSearching"
        :disabled="disabled"
        size="large"
        allow-clear
        enter-button="搜索"
        @search="handleSearch"
        @change="handleSearchChange"
        @focus="handleFocus"
        @blur="handleBlur"
        class="search-input"
      >
        <template #prefix>
          <i class="i-carbon-search text-gray-400"></i>
        </template>
      </a-input-search>
    </div>

    <!-- 搜索历史下拉 -->
    <div 
      v-if="showHistory && searchHistory.length > 0" 
      class="search-history-dropdown"
      :class="{ 'visible': showHistoryDropdown }"
    >
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <a-button 
          type="text" 
          size="small" 
          @click="clearHistory"
          class="clear-btn"
        >
          清除
        </a-button>
      </div>
      <div class="history-list">
        <div
          v-for="(item, index) in searchHistory"
          :key="index"
          class="history-item"
          @click="selectHistoryItem(item)"
        >
          <i class="i-carbon-time text-gray-400"></i>
          <span class="history-text">{{ item }}</span>
          <a-button
            type="text"
            size="small"
            @click.stop="removeHistoryItem(index)"
            class="remove-btn"
          >
            <i class="i-carbon-close text-gray-400"></i>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="showResults && searchResults.length > 0" class="search-results">
      <div class="results-header">
        <span class="results-count">找到 {{ totalResults }} 个结果</span>
        <a-button 
          v-if="hasMoreResults"
          type="link" 
          size="small"
          @click="loadMoreResults"
          :loading="loadingMore"
        >
          加载更多
        </a-button>
      </div>
      
      <div class="results-list">
        <div
          v-for="result in searchResults"
          :key="result.id"
          class="result-item"
          @click="handleResultClick(result)"
        >
          <div class="result-icon">
            <i :class="getFileIcon(result.type, result.name)" class="file-icon"></i>
          </div>
          <div class="result-content">
            <div class="result-name" v-html="result.highlight?.highlighted || result.name"></div>
            <div class="result-meta">
              <span class="result-type">{{ getFileTypeLabel(result.type) }}</span>
              <span v-if="result.file_size" class="result-size">{{ formatFileSize(result.file_size) }}</span>
              <span class="result-date">{{ formatDate(result.created_at) }}</span>
            </div>
          </div>
          <div class="result-score" v-if="result.relevance_score">
            <a-tag :color="getScoreColor(result.relevance_score)">
              {{ result.relevance_score }}%
            </a-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="showResults && searchResults.length === 0 && !isSearching" class="empty-results">
      <div class="empty-icon">
        <i class="i-carbon-search-locate text-gray-300 text-4xl"></i>
      </div>
      <div class="empty-text">
        <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关文件</h3>
        <p class="text-gray-600">尝试使用其他关键词搜索</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { debounce } from 'lodash-es'
import { fileApi } from '@/api/file'
import type { 
  FileSearchResponse, 
  SearchResultItem, 
  FileSearchParams 
} from '@/types'

// Props定义
interface Props {
  subjectId: number
  placeholder?: string
  disabled?: boolean
  showHistory?: boolean
  showResults?: boolean
  autoSearch?: boolean
  debounceDelay?: number
  maxHistoryItems?: number
  resultLimit?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索文件...',
  disabled: false,
  showHistory: true,
  showResults: true,
  autoSearch: true,
  debounceDelay: 300,
  maxHistoryItems: 10,
  resultLimit: 20
})

// Events定义
interface Emits {
  search: [keyword: string, results: SearchResultItem[]]
  resultClick: [result: SearchResultItem]
  historyChange: [history: string[]]
  focus: []
  blur: []
}

const emit = defineEmits<Emits>()

// 响应式数据
const searchKeyword = ref('')
const isSearching = ref(false)
const loadingMore = ref(false)
const showHistoryDropdown = ref(false)
const searchResults = ref<SearchResultItem[]>([])
const totalResults = ref(0)
const currentPage = ref(1)
const searchHistory = ref<string[]>([])

// 计算属性
const hasMoreResults = computed(() => {
  return searchResults.value.length < totalResults.value
})

// 本地存储键名
const STORAGE_KEY = `file-search-history-${props.subjectId}`

// 防抖搜索函数
const debouncedSearch = debounce(async (keyword: string) => {
  if (!keyword.trim() || keyword.length < 2) {
    searchResults.value = []
    totalResults.value = 0
    return
  }

  isSearching.value = true
  try {
    const params: FileSearchParams = {
      q: keyword.trim(),
      limit: props.resultLimit
    }

    const response = await fileApi.searchFiles(props.subjectId, params)
    const data = response.data.data

    searchResults.value = data.results
    totalResults.value = data.total
    currentPage.value = 1

    // 保存搜索历史
    saveToHistory(keyword.trim())

    // 触发搜索事件
    emit('search', keyword.trim(), data.results)
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
    totalResults.value = 0
  } finally {
    isSearching.value = false
  }
}, props.debounceDelay)

// 事件处理函数
const handleSearch = (value: string) => {
  searchKeyword.value = value
  if (props.autoSearch) {
    debouncedSearch(value)
  }
}

const handleSearchChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  searchKeyword.value = target.value

  if (props.autoSearch && target.value.trim()) {
    debouncedSearch(target.value)
  } else if (!target.value.trim()) {
    searchResults.value = []
    totalResults.value = 0
  }
}

const handleFocus = () => {
  showHistoryDropdown.value = true
  emit('focus')
}

const handleBlur = () => {
  // 延迟隐藏，允许点击历史项
  setTimeout(() => {
    showHistoryDropdown.value = false
  }, 200)
  emit('blur')
}

const handleResultClick = (result: SearchResultItem) => {
  emit('resultClick', result)
}

// 搜索历史管理
const loadHistory = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      searchHistory.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error)
    searchHistory.value = []
  }
}

const saveToHistory = (keyword: string) => {
  if (!keyword.trim()) return

  // 移除重复项
  const filtered = searchHistory.value.filter(item => item !== keyword)

  // 添加到开头
  filtered.unshift(keyword)

  // 限制数量
  if (filtered.length > props.maxHistoryItems) {
    filtered.splice(props.maxHistoryItems)
  }

  searchHistory.value = filtered

  // 保存到本地存储
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(searchHistory.value))
    emit('historyChange', searchHistory.value)
  } catch (error) {
    console.error('保存搜索历史失败:', error)
  }
}

const selectHistoryItem = (item: string) => {
  searchKeyword.value = item
  showHistoryDropdown.value = false
  if (props.autoSearch) {
    debouncedSearch(item)
  }
}

const removeHistoryItem = (index: number) => {
  searchHistory.value.splice(index, 1)
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(searchHistory.value))
    emit('historyChange', searchHistory.value)
  } catch (error) {
    console.error('更新搜索历史失败:', error)
  }
}

const clearHistory = () => {
  searchHistory.value = []
  try {
    localStorage.removeItem(STORAGE_KEY)
    emit('historyChange', [])
  } catch (error) {
    console.error('清除搜索历史失败:', error)
  }
}

// 加载更多结果
const loadMoreResults = async () => {
  if (!searchKeyword.value.trim() || loadingMore.value) return

  loadingMore.value = true
  try {
    const params: FileSearchParams = {
      q: searchKeyword.value.trim(),
      limit: props.resultLimit
    }

    // 这里可以扩展支持分页，当前API可能不支持
    // 暂时重新搜索获取更多结果
    const response = await fileApi.searchFiles(props.subjectId, params)
    const data = response.data.data

    // 合并结果（去重）
    const existingIds = new Set(searchResults.value.map(r => r.id))
    const newResults = data.results.filter(r => !existingIds.has(r.id))

    searchResults.value.push(...newResults)
    totalResults.value = data.total
  } catch (error) {
    console.error('加载更多结果失败:', error)
  } finally {
    loadingMore.value = false
  }
}

// 工具函数
const getFileIcon = (type: string, name: string) => {
  if (type === 'folder') {
    return 'i-carbon-folder text-blue-500'
  }

  const ext = name.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'md':
    case 'markdown':
      return 'i-carbon-document text-green-500'
    case 'txt':
      return 'i-carbon-document-blank text-gray-500'
    case 'pdf':
      return 'i-carbon-document-pdf text-red-500'
    case 'doc':
    case 'docx':
      return 'i-carbon-document-word-processor text-blue-600'
    default:
      return 'i-carbon-document text-gray-400'
  }
}

const getFileTypeLabel = (type: string) => {
  return type === 'folder' ? '文件夹' : '文件'
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const getScoreColor = (score: number) => {
  if (score >= 90) return 'green'
  if (score >= 70) return 'blue'
  if (score >= 50) return 'orange'
  return 'red'
}

// 生命周期
onMounted(() => {
  loadHistory()
})

onUnmounted(() => {
  debouncedSearch.cancel()
})

// 监听搜索关键词变化
watch(searchKeyword, (newValue) => {
  if (!newValue.trim()) {
    searchResults.value = []
    totalResults.value = 0
  }
})

// 暴露方法给父组件
defineExpose({
  search: (keyword: string) => {
    searchKeyword.value = keyword
    debouncedSearch(keyword)
  },
  clear: () => {
    searchKeyword.value = ''
    searchResults.value = []
    totalResults.value = 0
  },
  focus: () => {
    // 可以通过ref获取input并focus
  }
})
</script>

<style scoped>
.file-search {
  position: relative;
  width: 100%;
}

.search-input-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
}

/* 搜索历史下拉 */
.search-history-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.2s ease;
}

.search-history-dropdown.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.history-title {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.clear-btn {
  font-size: 12px;
  color: #999;
  padding: 0 4px;
  height: auto;
}

.history-list {
  max-height: 240px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f5f5f5;
}

.history-text {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.remove-btn {
  opacity: 0;
  transition: opacity 0.2s;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-item:hover .remove-btn {
  opacity: 1;
}

/* 搜索结果 */
.search-results {
  margin-top: 16px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.results-count {
  font-size: 14px;
  color: #666;
}

.results-list {
  space-y: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.result-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.result-icon {
  margin-right: 12px;
}

.file-icon {
  font-size: 20px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  word-break: break-all;
}

.result-name :deep(mark) {
  background-color: #fff2e8;
  color: #fa8c16;
  padding: 0 2px;
  border-radius: 2px;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.result-score {
  margin-left: 12px;
}

/* 空状态 */
.empty-results {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text h3 {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .result-item {
    padding: 8px;
  }

  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .search-history-dropdown {
    max-height: 200px;
  }
}
</style>
