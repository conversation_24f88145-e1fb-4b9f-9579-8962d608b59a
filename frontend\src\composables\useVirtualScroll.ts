import { ref, computed, onMounted, onUnmounted, nextTick, readonly, type Ref } from 'vue'

export interface VirtualScrollOptions {
  itemHeight: number | ((index: number) => number) // 支持固定高度或动态高度
  visibleCount?: number // 可见项目数量
  buffer?: number // 缓冲区大小
  threshold?: number // 滚动阈值
}

export interface VirtualScrollItem {
  id: string | number
  height?: number
  [key: string]: any
}

export function useVirtualScroll<T extends VirtualScrollItem>(
  items: Ref<T[]>,
  containerRef: Ref<HTMLElement | null>,
  options: VirtualScrollOptions
) {
  const {
    itemHeight,
    visibleCount = 20,
    buffer = 5,
    threshold = 100
  } = options

  // 响应式状态
  const scrollTop = ref(0)
  const containerHeight = ref(0)
  const isScrolling = ref(false)
  const scrollDirection = ref<'up' | 'down'>('down')

  // 滚动防抖定时器
  let scrollTimer: number | null = null
  let lastScrollTop = 0

  // 计算每个项目的高度
  const getItemHeight = (index: number): number => {
    if (typeof itemHeight === 'function') {
      return itemHeight(index)
    }
    return itemHeight
  }

  // 计算总高度
  const totalHeight = computed(() => {
    if (typeof itemHeight === 'function') {
      return items.value.reduce((total, _, index) => total + getItemHeight(index), 0)
    }
    return items.value.length * itemHeight
  })

  // 计算可见范围
  const visibleRange = computed(() => {
    if (!containerHeight.value || items.value.length === 0) {
      return { start: 0, end: 0 }
    }

    let start = 0
    let end = 0
    let accumulatedHeight = 0

    // 计算开始索引
    if (typeof itemHeight === 'function') {
      for (let i = 0; i < items.value.length; i++) {
        const height = getItemHeight(i)
        if (accumulatedHeight + height > scrollTop.value) {
          start = Math.max(0, i - buffer)
          break
        }
        accumulatedHeight += height
      }

      // 计算结束索引
      const targetHeight = scrollTop.value + containerHeight.value
      for (let i = start; i < items.value.length; i++) {
        if (accumulatedHeight > targetHeight) {
          end = Math.min(items.value.length, i + buffer)
          break
        }
        accumulatedHeight += getItemHeight(i)
      }
    } else {
      start = Math.max(0, Math.floor(scrollTop.value / itemHeight) - buffer)
      end = Math.min(
        items.value.length,
        Math.ceil((scrollTop.value + containerHeight.value) / itemHeight) + buffer
      )
    }

    return { start, end: Math.max(start, end) }
  })

  // 可见项目
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      ...item,
      index: start + index,
      offsetTop: getOffsetTop(start + index)
    }))
  })

  // 计算项目的偏移位置
  const getOffsetTop = (index: number): number => {
    if (typeof itemHeight === 'function') {
      let offset = 0
      for (let i = 0; i < index; i++) {
        offset += getItemHeight(i)
      }
      return offset
    }
    return index * itemHeight
  }

  // 占位符高度（上方）
  const offsetTop = computed(() => {
    return getOffsetTop(visibleRange.value.start)
  })

  // 占位符高度（下方）
  const offsetBottom = computed(() => {
    const { end } = visibleRange.value
    return totalHeight.value - getOffsetTop(end)
  })

  // 滚动事件处理
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    const newScrollTop = target.scrollTop

    // 更新滚动方向
    scrollDirection.value = newScrollTop > lastScrollTop ? 'down' : 'up'
    lastScrollTop = newScrollTop

    scrollTop.value = newScrollTop
    isScrolling.value = true

    // 防抖处理
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    scrollTimer = window.setTimeout(() => {
      isScrolling.value = false
    }, 150)
  }

  // 滚动到指定索引
  const scrollToIndex = async (index: number, behavior: ScrollBehavior = 'smooth') => {
    if (!containerRef.value || index < 0 || index >= items.value.length) {
      return
    }

    const targetScrollTop = getOffsetTop(index)
    containerRef.value.scrollTo({
      top: targetScrollTop,
      behavior
    })

    await nextTick()
  }

  // 滚动到指定项目
  const scrollToItem = async (itemId: string | number, behavior: ScrollBehavior = 'smooth') => {
    const index = items.value.findIndex(item => item.id === itemId)
    if (index !== -1) {
      await scrollToIndex(index, behavior)
    }
  }

  // 更新容器高度
  const updateContainerHeight = () => {
    if (containerRef.value) {
      containerHeight.value = containerRef.value.clientHeight
    }
  }

  // ResizeObserver 监听容器尺寸变化
  let resizeObserver: ResizeObserver | null = null

  // 初始化
  onMounted(() => {
    if (containerRef.value) {
      // 绑定滚动事件
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })

      // 初始化容器高度
      updateContainerHeight()

      // 监听容器尺寸变化
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          updateContainerHeight()
        })
        resizeObserver.observe(containerRef.value)
      }
    }
  })

  // 清理
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }

    if (resizeObserver) {
      resizeObserver.disconnect()
    }

    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  })

  return {
    // 状态
    scrollTop: readonly(scrollTop),
    containerHeight: readonly(containerHeight),
    isScrolling: readonly(isScrolling),
    scrollDirection: readonly(scrollDirection),

    // 计算属性
    totalHeight,
    visibleRange,
    visibleItems,
    offsetTop,
    offsetBottom,

    // 方法
    scrollToIndex,
    scrollToItem,
    updateContainerHeight,
    getItemHeight,
    getOffsetTop
  }
}

// 虚拟滚动样式工具
export const virtualScrollStyles = {
  container: {
    height: '100%',
    overflow: 'auto',
    position: 'relative' as const
  },
  wrapper: {
    position: 'relative' as const,
    width: '100%'
  },
  item: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0
  }
}
