<template>
  <div class="file-store-test p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">FileStore 扩展功能测试</h1>
    
    <!-- 测试控制面板 -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <h2 class="text-lg font-semibold mb-4">测试控制</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium mb-2">学科ID</label>
          <a-input-number v-model:value="testSubjectId" :min="1" placeholder="输入学科ID" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">父目录ID</label>
          <a-input-number v-model:value="testParentId" placeholder="输入父目录ID (可选)" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">搜索关键词</label>
          <a-input v-model:value="testSearchKeyword" placeholder="输入搜索关键词" />
        </div>
      </div>
      <div class="flex gap-2 mt-4">
        <a-button type="primary" @click="testGetFileList" :loading="fileStore.isFileListLoading">
          获取文件列表
        </a-button>
        <a-button @click="testSearchFiles" :loading="fileStore.isSearching">
          搜索文件
        </a-button>
        <a-button @click="testClearStates">
          清除状态
        </a-button>
      </div>
    </div>

    <!-- 状态显示面板 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 文件列表状态 -->
      <div class="bg-white rounded-lg shadow p-4">
        <h3 class="text-lg font-semibold mb-3">文件列表状态</h3>
        <div class="space-y-2 text-sm">
          <div><strong>当前学科ID:</strong> {{ fileStore.currentSubjectId || '未设置' }}</div>
          <div><strong>当前父目录ID:</strong> {{ fileStore.currentParentId || '根目录' }}</div>
          <div><strong>文件列表长度:</strong> {{ fileStore.fileList.length }}</div>
          <div><strong>是否加载中:</strong> {{ fileStore.isFileListLoading ? '是' : '否' }}</div>
          <div><strong>错误信息:</strong> {{ fileStore.fileListError || '无' }}</div>
          <div><strong>当前页码:</strong> {{ fileStore.currentPage }}</div>
          <div><strong>总页数:</strong> {{ fileStore.totalPages }}</div>
          <div><strong>文件总数:</strong> {{ fileStore.totalFiles }}</div>
          <div><strong>是否有文件列表:</strong> {{ fileStore.hasFileList ? '是' : '否' }}</div>
          <div><strong>是否在根目录:</strong> {{ fileStore.isRootDirectory ? '是' : '否' }}</div>
        </div>
      </div>

      <!-- 搜索状态 -->
      <div class="bg-white rounded-lg shadow p-4">
        <h3 class="text-lg font-semibold mb-3">搜索状态</h3>
        <div class="space-y-2 text-sm">
          <div><strong>搜索关键词:</strong> {{ fileStore.searchKeyword || '未设置' }}</div>
          <div><strong>搜索结果数量:</strong> {{ fileStore.searchResults.length }}</div>
          <div><strong>是否搜索中:</strong> {{ fileStore.isSearching ? '是' : '否' }}</div>
          <div><strong>搜索错误:</strong> {{ fileStore.searchError || '无' }}</div>
          <div><strong>是否有搜索结果:</strong> {{ fileStore.hasSearchResults ? '是' : '否' }}</div>
          <div><strong>是否在搜索模式:</strong> {{ fileStore.isInSearchMode ? '是' : '否' }}</div>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="bg-white rounded-lg shadow p-4 mt-6" v-if="fileStore.breadcrumbItems.length > 0">
      <h3 class="text-lg font-semibold mb-3">面包屑导航</h3>
      <div class="flex items-center space-x-2">
        <span v-for="(item, index) in fileStore.breadcrumbItems" :key="item.id" class="flex items-center">
          <span class="px-2 py-1 bg-gray-100 rounded text-sm">
            {{ item.name }} ({{ item.type }})
          </span>
          <span v-if="index < fileStore.breadcrumbItems.length - 1" class="mx-2 text-gray-400">/</span>
        </span>
      </div>
    </div>

    <!-- 文件列表显示 -->
    <div class="bg-white rounded-lg shadow p-4 mt-6" v-if="fileStore.hasFileList">
      <h3 class="text-lg font-semibold mb-3">文件列表 ({{ fileStore.fileList.length }} 项)</h3>
      <div class="space-y-2">
        <div v-for="file in fileStore.fileList" :key="file.id" 
             class="flex items-center justify-between p-2 border rounded hover:bg-gray-50">
          <div class="flex items-center space-x-3">
            <i :class="file.type === 'folder' ? 'i-carbon-folder text-blue-500' : 'i-carbon-document text-gray-500'"></i>
            <div>
              <div class="font-medium">{{ file.name }}</div>
              <div class="text-sm text-gray-500">
                ID: {{ file.id }} | 类型: {{ file.type }} | 
                创建时间: {{ new Date(file.created_at).toLocaleString() }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索结果显示 -->
    <div class="bg-white rounded-lg shadow p-4 mt-6" v-if="fileStore.hasSearchResults">
      <h3 class="text-lg font-semibold mb-3">搜索结果 ({{ fileStore.searchResults.length }} 项)</h3>
      <div class="space-y-2">
        <div v-for="result in fileStore.searchResults" :key="result.id" 
             class="flex items-center justify-between p-2 border rounded hover:bg-gray-50">
          <div class="flex items-center space-x-3">
            <i :class="result.type === 'folder' ? 'i-carbon-folder text-blue-500' : 'i-carbon-document text-gray-500'"></i>
            <div>
              <div class="font-medium" v-html="result.highlight.highlighted"></div>
              <div class="text-sm text-gray-500">
                ID: {{ result.id }} | 相关度: {{ result.relevance_score }} | 
                匹配位置: {{ result.highlight.matchIndex }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试日志 -->
    <div class="bg-white rounded-lg shadow p-4 mt-6">
      <h3 class="text-lg font-semibold mb-3">测试日志</h3>
      <div class="bg-gray-50 p-3 rounded max-h-40 overflow-y-auto">
        <div v-for="(log, index) in testLogs" :key="index" class="text-sm mb-1">
          <span class="text-gray-500">{{ log.time }}</span> - {{ log.message }}
        </div>
      </div>
      <a-button size="small" @click="clearLogs" class="mt-2">清除日志</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useFileStore } from '@/stores'

// Store实例
const fileStore = useFileStore()

// 测试参数
const testSubjectId = ref<number>(122)
const testParentId = ref<number | null>(null)
const testSearchKeyword = ref<string>('')

// 测试日志
const testLogs = ref<Array<{ time: string; message: string }>>([])

// 添加日志
const addLog = (message: string) => {
  testLogs.value.push({
    time: new Date().toLocaleTimeString(),
    message
  })
}

// 清除日志
const clearLogs = () => {
  testLogs.value = []
}

// 测试获取文件列表
const testGetFileList = async () => {
  try {
    addLog(`开始获取文件列表 - 学科ID: ${testSubjectId.value}, 父目录ID: ${testParentId.value}`)
    
    const result = await fileStore.getFileList(testSubjectId.value, {
      parentId: testParentId.value,
      page: 1,
      limit: 20
    })
    
    addLog(`文件列表获取成功 - 共 ${result.files.length} 个文件`)
  } catch (error: any) {
    addLog(`文件列表获取失败: ${error.message}`)
  }
}

// 测试搜索文件
const testSearchFiles = async () => {
  if (!testSearchKeyword.value.trim()) {
    addLog('请输入搜索关键词')
    return
  }
  
  try {
    addLog(`开始搜索文件 - 关键词: "${testSearchKeyword.value}"`)
    
    const result = await fileStore.searchFiles(testSubjectId.value, {
      q: testSearchKeyword.value,
      limit: 20
    })
    
    addLog(`文件搜索成功 - 共 ${result.results.length} 个结果`)
  } catch (error: any) {
    addLog(`文件搜索失败: ${error.message}`)
  }
}

// 测试清除状态
const testClearStates = () => {
  fileStore.clearFileListState()
  fileStore.clearSearchState()
  addLog('已清除所有状态')
}

// 初始化日志
addLog('FileStore 扩展功能测试页面已加载')
</script>
