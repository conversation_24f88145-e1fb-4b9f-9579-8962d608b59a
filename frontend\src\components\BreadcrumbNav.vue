<template>
  <div v-if="breadcrumbItems.length > 0" class="breadcrumb-nav" :class="containerClass">
    <!-- 桌面端完整面包屑 -->
    <div v-if="!isMobile" class="desktop-breadcrumb">
      <a-breadcrumb :separator="separator">
        <a-breadcrumb-item 
          v-for="(item, index) in visibleItems" 
          :key="item.id || `item-${index}`"
          :class="getBreadcrumbItemClass(item, index)"
          @click="handleItemClick(item, index)"
        >
          <div class="breadcrumb-item-content">
            <i :class="getItemIcon(item.type)" class="item-icon"></i>
            <span class="item-text" :title="item.name">{{ item.name }}</span>
            <span v-if="index === breadcrumbItems.length - 1" class="current-indicator"></span>
          </div>
        </a-breadcrumb-item>
        
        <!-- 省略指示器 -->
        <a-breadcrumb-item 
          v-if="hasHiddenItems" 
          class="ellipsis-item"
          @click="toggleExpanded"
        >
          <a-dropdown :trigger="['click']" placement="bottomLeft">
            <div class="ellipsis-content">
              <i class="i-carbon-overflow-menu-horizontal"></i>
              <span class="ellipsis-text">...</span>
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item 
                  v-for="(item, index) in hiddenItems" 
                  :key="item.id || `hidden-${index}`"
                  @click="handleItemClick(item, getHiddenItemIndex(index))"
                >
                  <i :class="getItemIcon(item.type)" class="mr-2"></i>
                  {{ item.name }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 移动端简化面包屑 -->
    <div v-else class="mobile-breadcrumb">
      <!-- 返回按钮 -->
      <div v-if="breadcrumbItems.length > 1" class="back-button" @click="handleBackClick">
        <i class="i-carbon-arrow-left"></i>
        <span>返回</span>
      </div>
      
      <!-- 当前位置 -->
      <div class="current-location">
        <i :class="getItemIcon(currentItem.type)" class="location-icon"></i>
        <span class="location-text" :title="currentItem.name">{{ currentItem.name }}</span>
      </div>
      
      <!-- 路径下拉菜单 -->
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <div class="path-dropdown-trigger">
          <i class="i-carbon-chevron-down"></i>
        </div>
        <template #overlay>
          <a-menu>
            <a-menu-item 
              v-for="(item, index) in breadcrumbItems" 
              :key="item.id || `mobile-${index}`"
              :class="{ 'current-item': index === breadcrumbItems.length - 1 }"
              @click="handleItemClick(item, index)"
            >
              <div class="mobile-menu-item">
                <i :class="getItemIcon(item.type)" class="mr-2"></i>
                <span>{{ item.name }}</span>
                <i v-if="index === breadcrumbItems.length - 1" class="i-carbon-checkmark ml-auto"></i>
              </div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="breadcrumb-loading">
      <a-spin size="small" />
      <span class="loading-text">加载路径中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { BreadcrumbItem } from '@/types'

// Props定义
interface Props {
  items: BreadcrumbItem[]
  maxItems?: number
  separator?: string
  loading?: boolean
  showIcons?: boolean
  clickable?: boolean
  theme?: 'light' | 'dark'
  size?: 'small' | 'medium' | 'large'
  responsive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 5,
  separator: '/',
  loading: false,
  showIcons: true,
  clickable: true,
  theme: 'light',
  size: 'medium',
  responsive: true
})

// Emits定义
interface Emits {
  itemClick: [item: BreadcrumbItem, index: number]
  backClick: [previousItem: BreadcrumbItem]
  pathChange: [newPath: BreadcrumbItem[]]
}

const emit = defineEmits<Emits>()

// 响应式数据
const expanded = ref(false)
const isMobile = ref(false)

// 计算属性
const breadcrumbItems = computed(() => props.items || [])

const currentItem = computed(() => {
  return breadcrumbItems.value[breadcrumbItems.value.length - 1] || { id: null, name: '根目录', type: 'subject', level: 0 }
})

const visibleItems = computed(() => {
  if (!props.responsive || expanded.value || breadcrumbItems.value.length <= props.maxItems) {
    return breadcrumbItems.value
  }
  
  // 显示首项、末项和中间的部分项
  const items = breadcrumbItems.value
  if (items.length <= 3) return items
  
  return [
    items[0], // 首项
    ...items.slice(-2) // 最后两项
  ]
})

const hiddenItems = computed(() => {
  if (!props.responsive || expanded.value || breadcrumbItems.value.length <= props.maxItems) {
    return []
  }
  
  const items = breadcrumbItems.value
  if (items.length <= 3) return []
  
  return items.slice(1, -2) // 中间被隐藏的项
})

const hasHiddenItems = computed(() => hiddenItems.value.length > 0)

const containerClass = computed(() => [
  'breadcrumb-container',
  `breadcrumb-${props.theme}`,
  `breadcrumb-${props.size}`,
  {
    'breadcrumb-loading-state': props.loading,
    'breadcrumb-mobile': isMobile.value,
    'breadcrumb-clickable': props.clickable
  }
])

// 工具方法
const getItemIcon = (type: string): string => {
  if (!props.showIcons) return ''
  
  switch (type) {
    case 'subject':
      return 'i-carbon-book'
    case 'folder':
      return 'i-carbon-folder'
    case 'file':
      return 'i-carbon-document'
    default:
      return 'i-carbon-home'
  }
}

const getBreadcrumbItemClass = (item: BreadcrumbItem, index: number) => [
  'breadcrumb-item',
  `breadcrumb-item-${item.type}`,
  {
    'breadcrumb-item-current': index === breadcrumbItems.value.length - 1,
    'breadcrumb-item-clickable': props.clickable && index < breadcrumbItems.value.length - 1,
    'breadcrumb-item-first': index === 0,
    'breadcrumb-item-last': index === breadcrumbItems.value.length - 1
  }
]

const getHiddenItemIndex = (hiddenIndex: number): number => {
  return hiddenIndex + 1 // 因为隐藏项从第二项开始
}

// 事件处理
const handleItemClick = (item: BreadcrumbItem, index: number) => {
  if (!props.clickable) return
  
  // 不允许点击当前项
  if (index === breadcrumbItems.value.length - 1) return
  
  emit('itemClick', item, index)
  
  // 触发路径变化事件
  const newPath = breadcrumbItems.value.slice(0, index + 1)
  emit('pathChange', newPath)
}

const handleBackClick = () => {
  if (breadcrumbItems.value.length <= 1) return
  
  const previousItem = breadcrumbItems.value[breadcrumbItems.value.length - 2]
  const previousIndex = breadcrumbItems.value.length - 2
  
  emit('backClick', previousItem)
  emit('itemClick', previousItem, previousIndex)
}

const toggleExpanded = () => {
  expanded.value = !expanded.value
}

// 响应式检测
const checkMobile = () => {
  if (!props.responsive) return
  isMobile.value = window.innerWidth < 768
}

const handleResize = () => {
  checkMobile()
}

// 生命周期
onMounted(() => {
  if (props.responsive) {
    checkMobile()
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  if (props.responsive) {
    window.removeEventListener('resize', handleResize)
  }
})

// 暴露给父组件的方法
const refresh = () => {
  expanded.value = false
  checkMobile()
}

const expandAll = () => {
  expanded.value = true
}

const collapseAll = () => {
  expanded.value = false
}

defineExpose({
  refresh,
  expandAll,
  collapseAll,
  isMobile: computed(() => isMobile.value),
  hasHiddenItems: computed(() => hasHiddenItems.value)
})
</script>

<style scoped>
/* 基础样式 */
.breadcrumb-nav {
  @apply w-full;
}

.breadcrumb-container {
  @apply bg-white rounded-lg shadow-sm p-4;
  transition: all 0.2s ease;
}

/* 主题样式 */
.breadcrumb-light {
  @apply bg-white text-gray-900;
}

.breadcrumb-dark {
  @apply bg-gray-800 text-white;
}

/* 尺寸样式 */
.breadcrumb-small {
  @apply p-2 text-sm;
}

.breadcrumb-medium {
  @apply p-4 text-base;
}

.breadcrumb-large {
  @apply p-6 text-lg;
}

/* 桌面端面包屑 */
.desktop-breadcrumb {
  @apply w-full;
}

.breadcrumb-item-content {
  @apply flex items-center space-x-2 cursor-pointer;
  transition: all 0.2s ease;
}

.item-icon {
  @apply text-sm;
}

.item-text {
  @apply truncate max-w-32;
}

.current-indicator {
  @apply w-2 h-2 bg-blue-500 rounded-full;
}

/* 省略指示器 */
.ellipsis-item {
  @apply cursor-pointer;
}

.ellipsis-content {
  @apply flex items-center space-x-1 text-gray-500 hover:text-blue-600;
  transition: color 0.2s ease;
}

.ellipsis-text {
  @apply text-lg font-bold;
}

/* 移动端面包屑 */
.mobile-breadcrumb {
  @apply flex items-center justify-between w-full;
}

.back-button {
  @apply flex items-center space-x-2 text-blue-600 cursor-pointer;
  transition: color 0.2s ease;
}

.back-button:hover {
  @apply text-blue-800;
}

.current-location {
  @apply flex items-center space-x-2 flex-1 mx-4;
}

.location-icon {
  @apply text-lg;
}

.location-text {
  @apply truncate font-medium;
}

.path-dropdown-trigger {
  @apply p-2 text-gray-500 hover:text-blue-600 cursor-pointer;
  transition: color 0.2s ease;
}

.mobile-menu-item {
  @apply flex items-center w-full;
}

.current-item {
  @apply bg-blue-50 text-blue-600;
}

/* 加载状态 */
.breadcrumb-loading {
  @apply flex items-center space-x-2 text-gray-500;
}

.loading-text {
  @apply text-sm;
}

/* 交互状态 */
.breadcrumb-item-clickable:hover .breadcrumb-item-content {
  @apply text-blue-600;
}

.breadcrumb-item-current .breadcrumb-item-content {
  @apply text-gray-900 font-medium cursor-default;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .breadcrumb-container {
    @apply p-3;
  }
  
  .item-text {
    @apply max-w-20;
  }
}

/* 无障碍支持 */
.breadcrumb-item-clickable {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded;
}

/* 动画效果 */
.breadcrumb-nav {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
