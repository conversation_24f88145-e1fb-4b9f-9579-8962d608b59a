<template>
  <div class="file-browser-test p-6">
    <h1 class="text-2xl font-bold mb-6">FileBrowser组件测试</h1>
    
    <!-- 测试控制面板 -->
    <div class="test-controls bg-gray-100 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-semibold mb-4">测试控制</h2>
      <div class="flex flex-wrap gap-4">
        <div>
          <label class="block text-sm font-medium mb-1">学科ID:</label>
          <a-input-number v-model:value="testSubjectId" :min="1" style="width: 120px" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">初始父目录ID:</label>
          <a-input-number v-model:value="testParentId" :min="0" style="width: 120px" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">高度:</label>
          <a-input v-model:value="testHeight" style="width: 120px" placeholder="auto" />
        </div>
        <div class="flex items-end">
          <a-button type="primary" @click="refreshBrowser">刷新组件</a-button>
        </div>
      </div>
      
      <div class="mt-4 flex flex-wrap gap-2">
        <a-switch v-model:checked="showBreadcrumb" checked-children="面包屑" un-checked-children="无面包屑" />
        <a-switch v-model:checked="enableSearch" checked-children="搜索" un-checked-children="无搜索" />
        <a-switch v-model:checked="enableVirtualScroll" checked-children="虚拟滚动" un-checked-children="普通滚动" />
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log bg-white border rounded-lg p-4 mb-6">
      <h3 class="text-md font-semibold mb-2">事件日志</h3>
      <div class="max-h-32 overflow-y-auto">
        <div v-for="(log, index) in eventLogs" :key="index" class="text-sm py-1 border-b">
          <span class="text-gray-500">{{ log.time }}</span> - 
          <span class="font-medium">{{ log.event }}</span>: 
          <span class="text-blue-600">{{ log.data }}</span>
        </div>
        <div v-if="eventLogs.length === 0" class="text-gray-500 text-sm">暂无事件</div>
      </div>
      <a-button size="small" @click="clearLogs" class="mt-2">清空日志</a-button>
    </div>

    <!-- FileBrowser组件 -->
    <div class="browser-container border rounded-lg">
      <FileBrowser
        ref="fileBrowserRef"
        :subject-id="testSubjectId"
        :initial-parent-id="testParentId || null"
        :height="testHeight"
        :show-breadcrumb="showBreadcrumb"
        :enable-search="enableSearch"
        :enable-virtual-scroll="enableVirtualScroll"
        @file-click="handleFileClick"
        @file-double-click="handleFileDoubleClick"
        @folder-enter="handleFolderEnter"
        @breadcrumb-click="handleBreadcrumbClick"
      />
    </div>

    <!-- 测试按钮 -->
    <div class="test-actions mt-6 space-x-4">
      <a-button @click="testRefresh">测试刷新</a-button>
      <a-button @click="testNavigateToRoot">导航到根目录</a-button>
      <a-button @click="testClearSearch">清空搜索</a-button>
      <a-button @click="testNavigateToFolder">导航到文件夹1</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import FileBrowser from '@/components/FileBrowser.vue'
import type { FileNode, BreadcrumbItem } from '@/types'

// 测试参数
const testSubjectId = ref(1)
const testParentId = ref<number | null>(null)
const testHeight = ref('600px')
const showBreadcrumb = ref(true)
const enableSearch = ref(true)
const enableVirtualScroll = ref(true)

// 组件引用
const fileBrowserRef = ref<InstanceType<typeof FileBrowser>>()

// 事件日志
interface EventLog {
  time: string
  event: string
  data: string
}

const eventLogs = ref<EventLog[]>([])

const addLog = (event: string, data: any) => {
  const time = new Date().toLocaleTimeString()
  const logData = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data)
  eventLogs.value.unshift({
    time,
    event,
    data: logData
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 事件处理
const handleFileClick = (file: FileNode) => {
  addLog('文件点击', `${file.name} (ID: ${file.id}, Type: ${file.type})`)
}

const handleFileDoubleClick = (file: FileNode) => {
  addLog('文件双击', `${file.name} (ID: ${file.id}, Type: ${file.type})`)
}

const handleFolderEnter = (folder: FileNode) => {
  addLog('进入文件夹', `${folder.name} (ID: ${folder.id})`)
}

const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
  addLog('面包屑点击', `${item.name} (Index: ${index}, Type: ${item.type})`)
}

// 测试方法
const refreshBrowser = () => {
  if (fileBrowserRef.value) {
    fileBrowserRef.value.refresh()
    addLog('手动刷新', '组件已刷新')
  }
}

const testRefresh = () => {
  if (fileBrowserRef.value) {
    fileBrowserRef.value.refresh()
    addLog('测试刷新', '调用refresh方法')
  }
}

const testNavigateToRoot = () => {
  if (fileBrowserRef.value) {
    fileBrowserRef.value.navigateToFolder(null)
    addLog('测试导航', '导航到根目录')
  }
}

const testClearSearch = () => {
  if (fileBrowserRef.value) {
    fileBrowserRef.value.clearSearch()
    addLog('测试清空搜索', '清空搜索关键词')
  }
}

const testNavigateToFolder = () => {
  if (fileBrowserRef.value) {
    fileBrowserRef.value.navigateToFolder(1)
    addLog('测试导航', '导航到文件夹ID: 1')
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

// 生命周期
onMounted(() => {
  addLog('组件挂载', 'FileBrowser测试页面已加载')
})
</script>

<style scoped>
.file-browser-test {
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  border: 1px solid #d9d9d9;
}

.event-log {
  font-family: 'Courier New', monospace;
}

.browser-container {
  min-height: 400px;
}
</style>
