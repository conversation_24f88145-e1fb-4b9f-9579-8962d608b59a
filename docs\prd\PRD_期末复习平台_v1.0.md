# 产品需求文档 (PRD) - 期末复习平台

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **产品名称** | 期末复习平台 |
| **产品版本** | v1.0 |
| **文档版本** | v1.0 |
| **创建日期** | 2025-01-28 |
| **最后更新** | 2025-01-28 |
| **负责人** | Emma (产品经理) |
| **审核人** | Mike (团队领袖) |
| **状态** | 待审核 |

### 版本历史
| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-01-28 | 基于草案重构，完善PRD标准结构 | Emma |

## 2. 背景与问题陈述

### 2.1 项目背景
在学术环境中，复习资料的分享和获取一直是一个重要需求。传统的资料分享方式（如文件传输、云盘分享）存在以下问题：
- 文件结构容易丢失，影响资料的逻辑性
- 缺乏统一的展示平台，资料分散难以管理
- Markdown格式的笔记无法直接在线阅读
- 图片资源路径问题导致显示异常

### 2.2 核心问题
1. **资料分享效率低**：管理员需要花费大量时间整理和上传资料
2. **获取门槛高**：访客需要下载、解压、安装软件才能查看资料
3. **结构保持困难**：现有平台无法完整保持原有的文件夹层级结构
4. **跨设备访问不便**：缺乏响应式的在线阅读体验

### 2.3 解决方案价值
通过构建一个专门的期末复习平台，实现：
- 一键批量上传，保持原有文件结构
- 在线Markdown渲染，无需下载即可阅读
- 跨设备响应式访问，随时随地学习
- 永久存储，长期可访问

## 3. 目标与成功指标

### 3.1 产品目标 (Objectives)
1. **提升分享效率**：将资料上传时间从小时级降低到分钟级
2. **降低获取门槛**：实现零门槛的在线资料访问
3. **保证结构完整性**：100%保持原有文件夹层级结构
4. **提供优质阅读体验**：支持多设备响应式阅读

### 3.2 关键结果 (Key Results)
| 指标类别 | 具体指标 | 目标值 | 测量方式 |
|----------|----------|--------|----------|
| **效率指标** | 文件上传成功率 | ≥95% | 系统日志统计 |
| **效率指标** | 平均上传处理时间 | ≤5分钟/100MB | 系统性能监控 |
| **使用指标** | 月活跃访客数 | ≥100人 | 访问日志分析 |
| **使用指标** | 平均会话时长 | ≥10分钟 | 用户行为分析 |
| **质量指标** | 页面加载时间 | ≤3秒 | 性能监控工具 |
| **质量指标** | 移动端兼容性 | 100% | 多设备测试 |

### 3.3 反向指标 (Counter Metrics)
- 服务器存储成本不应超过预算上限
- 系统维护时间不应影响正常访问
- 文件处理错误率应控制在5%以下

## 4. 用户画像与用户故事

### 4.1 目标用户画像

#### 主要用户：管理员
- **身份**：教师、助教、学长学姐
- **特征**：拥有大量结构化的复习资料，希望高效分享
- **技能水平**：基础计算机操作能力，熟悉文件管理
- **使用场景**：期末考试前、新学期开始时
- **核心需求**：快速上传、保持结构、长期存储

#### 次要用户：访客
- **身份**：在校学生、自学者
- **特征**：需要获取复习资料，希望便捷访问
- **技能水平**：基础网络浏览能力
- **使用场景**：复习备考期间、日常学习时
- **核心需求**：快速浏览、清晰阅读、移动访问

### 4.2 用户故事

#### 管理员用户故事
1. **学科创建**
   - 作为管理员，我希望能快速创建新的学科分类，以便为不同课程组织复习资料
   - 验收标准：点击按钮后能在30秒内完成学科创建

2. **批量上传**
   - 作为管理员，我希望能一次性上传整个文件夹，以便保持我原有的资料组织结构
   - 验收标准：支持选择文件夹，自动处理所有子文件和子文件夹

3. **内容管理**
   - 作为管理员，我希望系统能自动处理Markdown文件和图片，以便资料能正确显示给访客
   - 验收标准：上传后的Markdown文件能正确渲染，图片能正常显示

#### 访客用户故事
1. **资料浏览**
   - 作为访客，我希望能浏览所有可用的学科，以便找到我需要的复习资料
   - 验收标准：主页能清晰展示所有学科，点击后能进入对应页面

2. **结构化访问**
   - 作为访客，我希望能按照原有的文件夹结构浏览资料，以便理解资料的逻辑关系
   - 验收标准：文件夹层级与原始结构完全一致

3. **跨设备阅读**
   - 作为访客，我希望能在手机上正常阅读资料，以便随时随地学习
   - 验收标准：在移动设备上文字清晰可读，图片正常显示

## 5. 功能规格详述

### 5.1 学科管理模块

#### 功能点 FRD-01：创建学科
- **功能描述**：管理员可以创建新的学科分类
- **业务流程**：
  1. 管理员访问主页
  2. 点击"新建学科"按钮
  3. 在弹窗中输入学科名称
  4. 系统验证名称唯一性
  5. 创建成功，主页显示新学科卡片
- **输入参数**：学科名称（必填，1-50字符，不可重复）
- **输出结果**：学科ID、创建时间、显示状态
- **异常处理**：
  - 名称为空：提示"学科名称不能为空"
  - 名称重复：提示"学科名称已存在"
  - 创建失败：提示"创建失败，请重试"

#### 功能点 FRD-02：学科列表展示
- **功能描述**：在主页展示所有已创建的学科
- **业务流程**：
  1. 用户访问主页
  2. 系统加载所有学科数据
  3. 以卡片形式展示学科列表
  4. 显示学科名称、创建时间、文件数量
- **展示规则**：按创建时间倒序排列，每行显示3-4个卡片（响应式）

### 5.2 文件上传与处理模块

#### 功能点 FRD-03：文件夹批量上传
- **功能描述**：管理员可以上传包含Markdown文件和图片的文件夹
- **技术实现**：使用HTML5的webkitdirectory属性实现文件夹选择
- **业务流程**：
  1. 管理员进入指定学科页面
  2. 点击"上传笔记"按钮
  3. 系统弹出文件夹选择对话框
  4. 管理员选择本地文件夹
  5. 系统显示上传进度
  6. 处理完成后刷新页面显示结果
- **处理规则**：
  - 支持的文件格式：.md、.png、.jpg、.jpeg、.gif、.svg
  - 单次上传限制：500MB
  - 文件名编码：自动处理UTF-8编码
- **异常处理**：
  - 文件过大：提示"文件夹大小超过500MB限制"
  - 格式不支持：忽略不支持的文件，记录日志
  - 上传中断：支持断点续传或重新上传

#### 功能点 FRD-04：文件结构保留
- **功能描述**：完整保留上传文件夹的层级结构
- **实现方式**：
  - 前端：遍历FileList，记录每个文件的相对路径
  - 后端：根据相对路径重建文件夹结构
  - 数据库：使用树形结构存储文件层级关系
- **验证标准**：上传后的文件结构与本地文件夹结构100%一致

#### 功能点 FRD-05：Markdown渲染与图片处理
- **功能描述**：正确渲染Markdown文件并处理其中的图片引用
- **处理逻辑**：
  1. 解析Markdown文件内容
  2. 识别相对路径的图片引用（如：./images/pic.png）
  3. 将相对路径替换为服务器URL
  4. 使用Markdown解析器渲染HTML
- **支持的Markdown语法**：
  - 标题（H1-H6）
  - 列表（有序、无序）
  - 代码块（行内、块级）
  - 表格
  - 链接和图片
  - 粗体、斜体
  - 引用块

### 5.3 内容浏览模块

#### 功能点 FRD-06：访客浏览
- **功能描述**：任何用户都可以无需登录访问平台内容
- **访问控制**：完全开放，无需身份验证
- **浏览流程**：
  1. 访客打开网站首页
  2. 查看所有学科列表
  3. 点击学科进入文件列表
  4. 逐层浏览文件夹结构
  5. 点击Markdown文件阅读内容

#### 功能点 FRD-07：响应式布局
- **功能描述**：支持多设备访问的响应式设计
- **断点设置**：
  - 手机：< 768px
  - 平板：768px - 1024px
  - 桌面：> 1024px
- **适配规则**：
  - 手机：单列布局，大字体，触摸友好
  - 平板：双列布局，中等字体
  - 桌面：多列布局，标准字体
- **性能要求**：各设备上页面加载时间均不超过3秒

## 6. 范围定义

### 6.1 包含功能 (In Scope)
✅ **核心功能**
- 学科分类创建和管理
- 文件夹批量上传功能
- 完整的文件结构保留
- Markdown文件在线渲染
- 图片资源正确显示
- 响应式多设备访问
- 无需登录的公开访问

✅ **技术特性**
- SQLite数据库存储
- 文件永久存储
- 500MB上传限制
- 基础的错误处理
- 上传进度显示

### 6.2 排除功能 (Out of Scope)
❌ **用户系统**
- 用户注册和登录
- 用户权限管理
- 个人资料管理

❌ **交互功能**
- 评论和讨论
- 点赞和收藏
- 资料评分

❌ **高级功能**
- 全文搜索
- 资料下载
- 在线编辑
- 版本控制
- 协作编辑

❌ **运营功能**
- 数据统计面板
- 用户行为分析
- A/B测试
- 内容推荐

## 7. 依赖与风险

### 7.1 内部依赖
| 依赖项 | 负责人 | 预计完成时间 | 风险等级 |
|--------|--------|--------------|----------|
| 技术架构确认 | Bob | 2天 | 低 |
| 前端框架搭建 | Alex | 3天 | 中 |
| 后端API开发 | Alex | 5天 | 中 |
| 数据库设计实现 | Alex | 2天 | 低 |
| 文件上传功能 | Alex | 4天 | 高 |

### 7.2 外部依赖
| 依赖项 | 提供方 | 预计时间 | 风险等级 |
|--------|--------|----------|----------|
| 服务器环境 | 运维团队 | 1天 | 低 |
| 域名和SSL证书 | 运维团队 | 1天 | 低 |
| 文件存储空间 | 云服务商 | 即时 | 低 |

### 7.3 技术风险
| 风险项 | 影响程度 | 发生概率 | 缓解措施 |
|--------|----------|----------|----------|
| webkitdirectory兼容性 | 高 | 中 | 提供降级方案，支持单文件上传 |
| 大文件上传稳定性 | 高 | 中 | 实现分片上传和断点续传 |
| SQLite并发性能 | 中 | 低 | 监控性能，必要时迁移到PostgreSQL |
| 图片路径处理复杂性 | 中 | 中 | 充分测试各种路径格式 |
| 磁盘空间不足 | 高 | 低 | 设置存储监控和清理策略 |

### 7.4 业务风险
| 风险项 | 影响程度 | 发生概率 | 缓解措施 |
|--------|----------|----------|----------|
| 用户接受度低 | 高 | 低 | 提前进行用户调研和原型测试 |
| 资料版权问题 | 中 | 低 | 明确使用条款和免责声明 |
| 服务器成本超预算 | 中 | 中 | 设置存储限制和成本监控 |

## 8. 发布初步计划

### 8.1 开发阶段规划

#### 阶段一：MVP版本 (v0.1) - 预计2周
**目标**：实现核心功能的最小可用版本
- ✅ 学科创建和列表展示
- ✅ 基础的文件夹上传功能
- ✅ 简单的文件浏览
- ✅ 基础的Markdown渲染

**验收标准**：
- 能成功创建学科
- 能上传包含Markdown文件的文件夹
- 能在线查看Markdown内容

#### 阶段二：完整版本 (v1.0) - 预计1周
**目标**：完善所有PRD定义的功能
- ✅ 完整的文件结构保留
- ✅ 图片路径处理和显示
- ✅ 响应式布局适配
- ✅ 错误处理和用户反馈

**验收标准**：
- 所有FRD功能点100%实现
- 通过全面的功能测试
- 支持多设备访问

#### 阶段三：优化版本 (v1.1) - 预计1周
**目标**：性能优化和用户体验改进
- ✅ 上传性能优化
- ✅ 页面加载速度优化
- ✅ 用户界面美化
- ✅ 错误处理完善

### 8.2 测试策略
- **单元测试**：核心业务逻辑覆盖率>80%
- **集成测试**：API接口和数据库操作测试
- **端到端测试**：使用Playwright进行完整用户流程测试
- **兼容性测试**：主流浏览器和设备测试
- **性能测试**：大文件上传和高并发访问测试

### 8.3 发布计划
1. **内部测试**：开发团队内部验证
2. **小范围测试**：邀请5-10名目标用户测试
3. **灰度发布**：向部分用户开放访问
4. **全量发布**：正式对外发布

### 8.4 数据跟踪计划
**上线前准备**：
- 集成基础的访问统计
- 设置关键指标监控
- 建立错误日志收集

**上线后跟踪**：
- 每日监控关键指标
- 每周生成使用报告
- 每月进行用户反馈收集

## 9. 附录

### 9.1 技术栈确认
- **前端**：Vue3 + TypeScript + Ant Design Vue + UnoCSS + Vite + Vben Admin
- **后端**：Node.js + Koa/Express
- **数据库**：SQLite + better-sqlite3
- **部署**：Docker + Nginx

### 9.2 API接口概览
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/api/subjects` | POST | 创建新学科 |
| `/api/subjects` | GET | 获取所有学科列表 |
| `/api/subjects/{id}/files` | GET | 获取指定学科的文件结构 |
| `/api/subjects/{id}/upload` | POST | 上传文件夹到指定学科 |
| `/api/files/{fileId}` | GET | 获取单个md文件内容 |

### 9.3 数据库表结构
详细的数据库设计请参考技术架构文档，包含subjects表和file_nodes表的完整结构定义。

---

**文档状态**：✅ 已完成，等待审核
**下一步行动**：提交给Mike审核，通过后进入技术架构设计阶段