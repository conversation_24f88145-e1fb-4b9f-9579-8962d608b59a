import api from './index'
import type {
  ApiResponse,
  FileNode,
  UploadFileResponse,
  FileContentResponse,
  FileListResponse,
  BreadcrumbResponse,
  FileSearchResponse,
  FileListParams,
  FileSearchParams
} from '@/types'

export const fileApi = {
  // 上传文件到指定学科
  async uploadFile(subjectId: number, file: File): Promise<ApiResponse<UploadFileResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    return api.post(`/subjects/${subjectId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 根据文件ID获取文件内容
  async getFileContent(fileId: number): Promise<ApiResponse<FileContentResponse>> {
    return api.get(`/files/${fileId}/content`)
  },

  // 根据文件ID获取文件信息
  async getFileInfo(fileId: number): Promise<ApiResponse<FileNode>> {
    return api.get(`/files/${fileId}`)
  },

  // Sprint 03 新增API - 获取学科文件列表
  async getFileList(subjectId: number, params?: FileListParams): Promise<ApiResponse<FileListResponse>> {
    const searchParams = new URLSearchParams()

    if (params?.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params?.type) {
      searchParams.append('type', params.type)
    }
    if (params?.parentId) {
      searchParams.append('parentId', params.parentId.toString())
    }
    if (params?.search) {
      searchParams.append('search', params.search)
    }

    const queryString = searchParams.toString()
    const url = queryString ? `/subjects/${subjectId}/files?${queryString}` : `/subjects/${subjectId}/files`

    return api.get(url)
  },

  // Sprint 03 新增API - 获取面包屑导航
  async getBreadcrumb(fileId: number): Promise<ApiResponse<BreadcrumbResponse>> {
    return api.get(`/files/${fileId}/breadcrumb`)
  },

  // Sprint 03 新增API - 文件搜索
  async searchFiles(subjectId: number, params: FileSearchParams): Promise<ApiResponse<FileSearchResponse>> {
    const searchParams = new URLSearchParams()

    // 搜索关键词是必需的
    searchParams.append('q', params.q)

    if (params.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params.type) {
      searchParams.append('type', params.type)
    }

    return api.get(`/subjects/${subjectId}/search?${searchParams.toString()}`)
  }
}
