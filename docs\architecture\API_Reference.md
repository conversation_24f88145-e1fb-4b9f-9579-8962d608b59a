# API 参考文档

## 文档信息
- **版本**: v1.4.0
- **最后更新**: 2025-07-30
- **维护者**: <PERSON> (架构师) + <PERSON> (工程师)
- **状态**: Sprint 03完成 - 文件浏览功能全面实现，包含FileBrowser、FileSearch、BreadcrumbNav组件及完整API集成

## 概述
本文档提供期末复习平台的完整API参考，包括所有端点、请求/响应格式、认证方式和错误处理。

## 基础信息

### 基础URL
```
开发环境: http://localhost:3001
生产环境: https://api.qimofuxi.com
```

### 认证方式
- 当前版本: 无认证（开发阶段）
- 计划实现: JWT Token认证、API Key认证

### 通用响应格式
```json
{
  "success": boolean,
  "data": object | array,
  "message": string,
  "timestamp": string,
  "requestId": string,
  "responseTime": string
}
```

## API 端点

### 系统管理 API

#### 健康检查 ✅
- **端点**: `GET /health`
- **描述**: 检查API服务运行状态
- **状态**: 已实现
- **响应示例**:
```json
{
  "success": true,
  "message": "期末复习平台API服务运行正常",
  "timestamp": "2025-07-28T05:17:02.596Z",
  "version": "1.0.0"
}
```

### 学科管理 API ✅

#### 文件上传 ✅
- **端点**: `POST /api/subjects/:id/upload`
- **描述**: 上传Markdown文件到指定学科
- **状态**: 已实现并优化 (v0.9.3)
- **请求格式**: multipart/form-data
- **参数**:
  - id: 学科ID（路径参数，整数）
  - file: 上传的文件（表单字段）
- **文件限制**:
  - 类型: .md, .markdown
  - 大小: 最大10MB
  - 编码: UTF-8
- **响应示例** (v0.9.3优化):
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "id": 9,
    "name": "test_ui_upload_1753770828146.md",
    "originalName": "test_ui_upload.md",
    "size": 500,
    "mimeType": "application/octet-stream",
    "subjectId": 108,
    "uploadTime": "2025-07-29T06:33:48.156Z"
  },
  "timestamp": "2025-07-29T06:33:48.156Z",
  "requestId": "req-upload-9",
  "responseTime": "145ms"
}
```

**v0.9.3 API优化说明**:
- **响应数据结构优化**: 确保文件ID正确返回，支持前端自动跳转到文件详情页
- **错误处理增强**: 提供更详细的错误信息，便于前端调试和用户反馈
- **性能监控**: 添加响应时间监控，优化大文件上传性能
- **数据一致性**: 确保文件上传后立即可通过GET /api/files/:fileId访问

#### 文件内容获取 ✅
- **端点**: `GET /api/files/:fileId`
- **描述**: 根据文件ID获取Markdown文件内容和元信息
- **状态**: 已实现
- **参数**:
  - fileId: 文件ID（路径参数，整数）
- **响应示例**:
```json
{
  "success": true,
  "message": "获取文件信息成功",
  "data": {
    "id": 1,
    "subject_id": 1,
    "parent_id": null,
    "name": "example_1640995200000.md",
    "type": "file",
    "content": "# 示例文档\n\n这是文件内容...",
    "file_path": "uploads/1/example_1640995200000.md",
    "file_size": 1024,
    "mime_type": "text/markdown",
    "created_at": "2025-01-29T10:00:00.000Z",
    "updated_at": "2025-01-29T10:00:00.000Z"
  },
  "timestamp": "2025-01-29T10:00:00.000Z",
  "requestId": "req-124",
  "responseTime": "85ms"
}
```

#### 文件内容获取 ✅
- **端点**: `GET /api/files/:fileId/content`
- **描述**: 获取指定文件的完整内容和元信息
- **状态**: 已实现
- **参数**:
  - fileId: 文件ID（路径参数，整数）
- **响应示例**:
```json
{
  "success": true,
  "message": "获取文件内容成功",
  "data": {
    "id": 5,
    "name": "simple_test_1753758828338.md",
    "content": "# 测试文件\n\n这是一个测试Markdown文件。\n\n## 内容\n- 项目1\n- 项目2",
    "type": "file",
    "filePath": "uploads\\1\\simple_test_1753758828338.md",
    "fileSize": 82,
    "mimeType": "text/markdown",
    "createdAt": "2025-07-29 03:13:48",
    "updatedAt": "2025-07-29 03:13:48"
  },
  "timestamp": "2025-07-29T03:14:14.826Z",
  "requestId": "unknown",
  "responseTime": "0ms"
}
```

#### 学科文件列表获取 (扩展) ✅ - Sprint 03
- **端点**: `GET /api/subjects/:id/files`
- **描述**: 获取指定学科的文件列表，支持分页、过滤、层级浏览
- **状态**: 已实现 (v0.9.5)
- **参数**:
  - id: 学科ID（路径参数，整数）
  - page: 页码（查询参数，可选，默认1）
  - limit: 每页数量（查询参数，可选，默认50）
  - type: 文件类型过滤（查询参数，可选，'file'或'folder'）
  - parentId: 父目录ID（查询参数，可选，用于层级浏览）
  - search: 搜索关键词（查询参数，可选）
- **响应示例**:
```json
{
  "success": true,
  "message": "获取文件列表成功",
  "data": {
    "subjectId": 1,
    "parentId": null,
    "files": [
      {
        "id": 1,
        "subject_id": 1,
        "parent_id": null,
        "name": "数学复习资料",
        "type": "folder",
        "file_path": null,
        "file_size": null,
        "mime_type": null,
        "created_at": "2025-07-29 02:36:46",
        "updated_at": "2025-07-29 02:36:46"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 3,
      "totalPages": 1,
      "hasNextPage": false,
      "hasPrevPage": false
    },
    "filters": {
      "search": null,
      "type": null
    }
  },
  "timestamp": "2025-07-29T13:23:12.950Z",
  "responseTime": "2ms"
}
```

#### 面包屑导航获取 ✅ - Sprint 03
- **端点**: `GET /api/files/:id/breadcrumb`
- **描述**: 获取指定文件/文件夹的面包屑导航路径
- **状态**: 已实现 (v0.9.5)
- **参数**:
  - id: 文件/文件夹ID（路径参数，整数）
- **响应示例**:
```json
{
  "success": true,
  "message": "获取面包屑导航成功",
  "data": {
    "nodeId": 1,
    "subjectId": 1,
    "breadcrumb": [
      {
        "id": null,
        "name": "数学",
        "type": "subject",
        "level": 1
      },
      {
        "id": 1,
        "name": "数学复习资料",
        "type": "folder",
        "level": 0
      }
    ],
    "currentNode": {
      "id": 1,
      "name": "数学复习资料",
      "type": "folder"
    }
  },
  "timestamp": "2025-07-29T13:23:55.640Z",
  "responseTime": "49ms"
}
```

#### 文件搜索 ✅ - Sprint 03
- **端点**: `GET /api/subjects/:id/search`
- **描述**: 在指定学科中搜索文件，支持智能搜索和结果高亮
- **状态**: 已实现 (v0.9.5)
- **参数**:
  - id: 学科ID（路径参数，整数）
  - q: 搜索关键词（查询参数，必需，至少2个字符）
  - limit: 结果数量限制（查询参数，可选，默认20）
  - type: 文件类型过滤（查询参数，可选）
- **响应示例**:
```json
{
  "success": true,
  "message": "搜索完成",
  "data": {
    "subjectId": 1,
    "keyword": "test",
    "results": [
      {
        "id": 5,
        "subject_id": 1,
        "parent_id": null,
        "name": "test_persistence_1753785012223.md",
        "type": "file",
        "file_path": "uploads\\1\\test_persistence_1753785012223.md",
        "file_size": 100,
        "mime_type": "text/markdown",
        "created_at": "2025-07-29 10:30:12",
        "updated_at": "2025-07-29 10:30:12",
        "relevance_score": 90,
        "highlight": {
          "original": "test_persistence_1753785012223.md",
          "highlighted": "<mark>test</mark>_persistence_1753785012223.md",
          "matchIndex": 0,
          "matchLength": 4
        }
      }
    ],
    "total": 2,
    "searchOptions": {
      "type": "all",
      "includeContent": false,
      "limit": 20
    }
  },
  "timestamp": "2025-07-29T13:24:23.382Z",
  "responseTime": "9ms"
}
```

#### 获取所有学科
- **端点**: `GET /api/subjects`
- **描述**: 获取所有学科列表
- **状态**: 已实现
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "数学",
      "description": "高等数学、线性代数、概率论等数学相关课程",
      "created_at": "2025-07-28 04:49:18",
      "updated_at": "2025-07-28 04:49:18"
    }
  ],
  "total": 1,
  "message": "获取学科列表成功",
  "timestamp": "2025-07-28T05:17:02.639Z",
  "requestId": "unknown",
  "responseTime": "27ms"
}
```

#### 创建学科
- **端点**: `POST /api/subjects`
- **描述**: 创建新学科
- **状态**: 已实现
- **请求体**:
```json
{
  "name": "学科名称",
  "description": "学科描述（可选）"
}
```
- **验证规则**:
  - name: 必填，1-50字符，只能包含中文、英文、数字、空格、连字符和下划线
  - description: 可选，最多500字符
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "name": "新学科",
    "description": "这是一个测试学科"
  },
  "message": "创建学科成功",
  "timestamp": "2025-07-28T05:17:02.666Z",
  "requestId": "unknown",
  "responseTime": "8ms"
}
```

#### 获取学科详情
- **端点**: `GET /api/subjects/:id`
- **描述**: 根据ID获取学科详细信息
- **状态**: 已实现
- **参数**:
  - id: 学科ID（整数）
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "数学",
    "description": "高等数学、线性代数、概率论等数学相关课程",
    "created_at": "2025-07-28 04:49:18",
    "updated_at": "2025-07-28 04:49:18"
  },
  "message": "获取学科详情成功",
  "timestamp": "2025-07-28T05:17:02.666Z",
  "requestId": "unknown",
  "responseTime": "5ms"
}
```

### 用户管理 API
#### 用户注册
- **端点**: `POST /auth/register`
- **描述**: 用户注册接口
- **状态**: 待实现

#### 用户登录
- **端点**: `POST /auth/login`
- **描述**: 用户登录接口
- **状态**: 待实现

### 复习内容管理 API
#### 获取复习材料列表
- **端点**: `GET /materials`
- **描述**: 获取复习材料列表
- **状态**: 待实现

#### 创建复习材料
- **端点**: `POST /materials`
- **描述**: 创建新的复习材料
- **状态**: 待实现

### 学习进度 API
#### 获取学习进度
- **端点**: `GET /progress/:userId`
- **描述**: 获取用户学习进度
- **状态**: 待实现

#### 更新学习进度
- **端点**: `PUT /progress/:userId`
- **描述**: 更新用户学习进度
- **状态**: 待实现

## 错误处理

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2025-07-28T05:17:02.666Z",
  "requestId": "unknown"
}
```

### 已实现错误代码
| 错误代码 | HTTP状态码 | 描述 | 状态 |
|---------|-----------|------|------|
| VALIDATION_ERROR | 400 | 输入验证失败 | ✅ |
| SUBJECT_NAME_EXISTS | 400 | 学科名称已存在 | ✅ |
| SUBJECT_NOT_FOUND | 404 | 学科不存在 | ✅ |
| DATABASE_ERROR | 500 | 数据库操作失败 | ✅ |
| NO_FILE_UPLOADED | 400 | 未选择上传文件 | ✅ |
| INVALID_FILE_TYPE | 400 | 文件类型不支持 | ✅ |
| INVALID_MIME_TYPE | 400 | 文件MIME类型不支持 | ✅ |
| FILE_TOO_LARGE | 400 | 文件大小超过限制 | ✅ |
| INVALID_FILENAME | 400 | 文件名包含不安全字符 | ✅ |
| FILENAME_TOO_LONG | 400 | 文件名过长 | ✅ |
| FILE_NOT_FOUND | 404 | 文件不存在 | ✅ |
| UPLOAD_ERROR | 500 | 文件上传失败 | ✅ |

## 前后端数据联调验证

### 联调测试结果

**测试场景**: 创建学科API的完整数据流验证

**测试步骤**:
1. 前端表单填写：学科名称"前后端数据联调测试学科"
2. 前端API调用：POST /api/subjects
3. 后端数据处理：验证、存储、响应
4. 前端状态更新：UI刷新、用户反馈

**验证结果**:
```json
// 请求数据
{
  "name": "前后端数据联调测试学科",
  "description": "这是一个用于验证前后端数据联调功能的测试学科，包含完整的描述信息和功能验证要点。"
}

// 响应数据
{
  "success": true,
  "data": {
    "id": 105,
    "name": "前后端数据联调测试学科",
    "description": "这是一个用于验证前后端数据联调功能的测试学科，包含完整的描述信息和功能验证要点。",
    "created_at": "2025-01-28T12:34:56.789Z",
    "updated_at": "2025-01-28T12:34:56.789Z"
  },
  "message": "学科创建成功"
}
```

### 性能指标

**API响应时间**:
- GET /api/subjects: 平均 85ms
- POST /api/subjects: 平均 120ms
- GET /api/subjects/:id: 平均 65ms

**数据库查询性能** (v0.9.4优化):
- 文件层级浏览查询: 6ms (目标<300ms) ✅
- 文件搜索查询: 2ms (目标<500ms) ✅
- 分页查询: 1ms (目标<1000ms) ✅
- 复合条件查询: 1ms ✅
- 平均查询时间: 2.20ms (性能优秀级别)

**索引优化效果**:
- ✅ 查询优化器正确使用idx_file_nodes_subject_parent索引
- ✅ 查询优化器正确使用idx_file_nodes_subject_name索引
- ✅ 所有关键查询都通过索引优化，避免全表扫描
- ✅ 为Sprint 03文件浏览功能提供高性能数据库支撑

**数据一致性验证**:
- ✅ 前端显示数据与数据库存储数据完全一致
- ✅ 页面刷新后数据持久化正常
- ✅ 学科总数正确更新（104 → 105）
- ✅ 新创建学科正确显示为#105

### 错误处理验证

**已验证的错误场景**:
- ✅ 网络连接错误：显示"网络连接失败"提示
- ✅ 服务器错误：显示"服务器内部错误"提示
- ✅ 验证错误：显示具体的字段验证失败信息
- ✅ 重复数据：显示"学科名称已存在"提示

### Playwright自动化测试覆盖 ✅

文件管理API已通过完整的Playwright自动化测试验证：

#### 测试场景覆盖
- **正常场景**: 文件上传(.md/.markdown)、文件信息获取、文件内容获取
- **错误场景**: 8种错误类型的完整验证
- **边界条件**: 文件名长度、空文件、边界ID等极端情况
- **性能测试**: 大文件上传和并发请求性能验证
- **数据一致性**: 上传后立即获取的数据一致性验证

#### 测试执行方式
```bash
# 运行文件API测试
npx playwright test tests/api/files.test.js

# 查看测试报告
npx playwright show-report

# 调试模式
npx playwright test tests/api/files.test.js --debug
```

#### 测试文件位置
- 测试文件: `backend/tests/api/files.test.js`
- 配置文件: `backend/playwright.config.js`
- 测试报告: `backend/playwright-report/`

## 前端集成指南 ✅

### FileUploader组件集成
前端已实现完整的文件上传组件，位于`frontend/src/components/FileUploader.vue`：

#### 组件使用示例
```vue
<template>
  <FileUploader
    :subject-id="subjectId"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
  />
</template>

<script setup lang="ts">
import { FileUploader } from '@/components'
import type { FileNode } from '@/types'

const handleUploadSuccess = (file: FileNode) => {
  console.log('文件上传成功:', file)
}

const handleUploadError = (error: string) => {
  console.error('文件上传失败:', error)
}
</script>
```

#### API调用封装
前端API调用已封装在`frontend/src/api/file.ts`：

```typescript
export const fileApi = {
  // 上传文件
  async uploadFile(subjectId: number, file: File) {
    const formData = new FormData()
    formData.append('file', file)

    return api.post(`/subjects/${subjectId}/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 获取文件内容
  async getFileContent(fileId: number) {
    return api.get(`/files/${fileId}/content`)
  },

  // 获取文件信息
  async getFileInfo(fileId: number) {
    return api.get(`/files/${fileId}`)
  }
}
```

### 文件浏览组件API集成 (v1.4.0新增) ✅

前端已实现完整的文件浏览组件体系，包含FileBrowser、FileSearch、BreadcrumbNav等核心组件：

#### FileBrowser组件API集成

**组件使用示例**：
```vue
<template>
  <FileBrowser
    :subject-id="subjectId"
    :initial-parent-id="parentId"
    :height="600"
    :show-breadcrumb="true"
    :enable-search="true"
    :enable-virtual-scroll="true"
    @file-click="handleFileClick"
    @folder-enter="handleFolderEnter"
    @breadcrumb-click="handleBreadcrumbClick"
  />
</template>

<script setup lang="ts">
import { FileBrowser } from '@/components'
import type { FileNode, BreadcrumbItem } from '@/types'

const handleFileClick = (file: FileNode) => {
  console.log('文件点击:', file)
}

const handleFolderEnter = (folder: FileNode) => {
  console.log('进入文件夹:', folder)
}

const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
  console.log('面包屑点击:', item, index)
}
</script>
```

**API调用封装扩展**：
```typescript
export const fileApi = {
  // 获取文件列表 (支持层级浏览)
  async getFileList(subjectId: number, params?: FileListParams) {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.type) queryParams.append('type', params.type)
    if (params?.parentId) queryParams.append('parentId', params.parentId.toString())
    if (params?.search) queryParams.append('search', params.search)

    return api.get(`/subjects/${subjectId}/files?${queryParams}`)
  },

  // 搜索文件
  async searchFiles(subjectId: number, params: FileSearchParams) {
    const queryParams = new URLSearchParams()
    queryParams.append('q', params.q)
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.type) queryParams.append('type', params.type)

    return api.get(`/subjects/${subjectId}/search?${queryParams}`)
  },

  // 获取面包屑导航
  async getBreadcrumb(fileId: number) {
    return api.get(`/files/${fileId}/breadcrumb`)
  },

  // 上传文件
  async uploadFile(subjectId: number, file: File) {
    const formData = new FormData()
    formData.append('file', file)

    return api.post(`/subjects/${subjectId}/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // 获取文件内容
  async getFileContent(fileId: number) {
    return api.get(`/files/${fileId}/content`)
  },

  // 获取文件信息
  async getFileInfo(fileId: number) {
    return api.get(`/files/${fileId}`)
  }
}
```

#### FileSearch组件API集成

**搜索功能特性**：
- **实时搜索**: 防抖搜索，避免频繁API调用
- **搜索历史**: 本地存储搜索历史记录
- **结果高亮**: 关键词高亮显示
- **智能排序**: 按相关度评分排序

**组件使用示例**：
```vue
<template>
  <FileSearch
    :subject-id="subjectId"
    :placeholder="'搜索文件...'"
    :show-history="true"
    :show-results="true"
    :auto-search="true"
    :debounce-delay="300"
    @search="handleSearch"
    @result-click="handleResultClick"
  />
</template>

<script setup lang="ts">
import { FileSearch } from '@/components'
import type { SearchResultItem } from '@/types'

const handleSearch = (keyword: string, results: SearchResultItem[]) => {
  console.log('搜索结果:', keyword, results)
}

const handleResultClick = (result: SearchResultItem) => {
  console.log('点击搜索结果:', result)
}
</script>
```

#### BreadcrumbNav组件API集成

**导航功能特性**：
- **智能省略**: 长路径自动省略中间项
- **响应式设计**: 移动端简化显示
- **主题支持**: 浅色/深色主题切换
- **交互优化**: 悬停效果和键盘导航

**组件使用示例**：
```vue
<template>
  <BreadcrumbNav
    :items="breadcrumbItems"
    :max-items="5"
    separator="/"
    theme="light"
    size="medium"
    :show-icons="true"
    :clickable="true"
    :responsive="true"
    @item-click="handleItemClick"
    @back-click="handleBackClick"
    @path-change="handlePathChange"
  />
</template>

<script setup lang="ts">
import { BreadcrumbNav } from '@/components'
import type { BreadcrumbItem } from '@/types'

const handleItemClick = (item: BreadcrumbItem, index: number) => {
  console.log('面包屑项点击:', item, index)
}

const handleBackClick = (previousItem: BreadcrumbItem) => {
  console.log('返回上级:', previousItem)
}

const handlePathChange = (newPath: BreadcrumbItem[]) => {
  console.log('路径变化:', newPath)
}
</script>
```

#### 虚拟滚动性能优化

**useVirtualScroll组合式函数**：
```typescript
import { useVirtualScroll } from '@/composables/useVirtualScroll'

// 在FileBrowser组件中使用
const {
  containerRef,
  listRef,
  visibleItems,
  scrollTop,
  handleScroll
} = useVirtualScroll({
  items: displayFiles,
  itemHeight: props.virtualScrollItemHeight,
  visibleCount: props.virtualScrollVisibleCount,
  buffer: 5
})

// 性能优化特性
const handleVirtualScroll = (event: Event) => {
  handleScroll(event)

  // 预加载逻辑
  const target = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = target

  if (scrollHeight - scrollTop - clientHeight < 100) {
    loadMoreFiles()
  }
}
```

#### 状态管理集成

**FileStore状态管理**：
```typescript
// 文件浏览状态
const fileList = ref<FileNode[]>([])
const currentSubjectId = ref<number | null>(null)
const currentParentId = ref<number | null>(null)
const isFileListLoading = ref(false)

// 搜索状态
const searchResults = ref<SearchResultItem[]>([])
const searchKeyword = ref('')
const isSearching = ref(false)

// 导航状态
const breadcrumbItems = ref<BreadcrumbItem[]>([])

// 分页状态
const currentPage = ref(1)
const totalFiles = ref(0)
const totalPages = ref(0)
```

#### 集成特性
- ✅ 支持拖拽和点击上传
- ✅ 实时上传进度显示
- ✅ 文件类型和大小验证
- ✅ 友好的错误处理和状态反馈
- ✅ 响应式设计适配各种屏幕尺寸
- ✅ 与Ant Design Vue设计系统一致

### 计划实现错误代码
| 错误代码 | HTTP状态码 | 描述 | 状态 |
|---------|-----------|------|------|
| 1002 | 401 | 未授权访问 | 待实现 |
| 1003 | 403 | 权限不足 | 待实现 |

## 更新日志
- 2025-07-29: 添加前端FileUploader组件集成指南和API调用封装
- 2025-07-28: 完成学科管理API文档，添加健康检查和错误处理规范
- 2025-01-28: 创建API文档模板框架

---
**注意**: 此文档将随着开发进度持续更新，请关注版本变更。