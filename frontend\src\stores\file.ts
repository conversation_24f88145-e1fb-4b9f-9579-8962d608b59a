import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fileApi } from '@/api/file'
import type {
  FileContentResponse,
  FileNode,
  FileListResponse,
  BreadcrumbResponse,
  FileSearchResponse,
  BreadcrumbItem,
  SearchResultItem,
  FileListParams,
  FileSearchParams
} from '@/types'

export const useFileStore = defineStore('file', () => {
  // 原有状态 - 文件内容相关
  const currentFile = ref<FileContentResponse | null>(null)
  const isLoading = ref(false)
  const hasError = ref(false)
  const errorMessage = ref('')

  // 新增状态 - 文件浏览相关
  const fileList = ref<FileNode[]>([]) // 当前目录的文件列表
  const currentSubjectId = ref<number | null>(null) // 当前学科ID
  const currentParentId = ref<number | null>(null) // 当前父目录ID
  const breadcrumbItems = ref<BreadcrumbItem[]>([]) // 面包屑导航
  const isFileListLoading = ref(false) // 文件列表加载状态
  const fileListError = ref('') // 文件列表错误信息

  // 搜索相关状态
  const searchResults = ref<SearchResultItem[]>([]) // 搜索结果
  const searchKeyword = ref('') // 当前搜索关键词
  const isSearching = ref(false) // 搜索加载状态
  const searchError = ref('') // 搜索错误信息

  // 分页相关状态
  const currentPage = ref(1) // 当前页码
  const pageSize = ref(20) // 每页大小
  const totalFiles = ref(0) // 文件总数
  const totalPages = ref(0) // 总页数

  // 原有计算属性 - 文件内容相关
  const fileContent = computed(() => currentFile.value?.data?.content || '')
  const fileName = computed(() => currentFile.value?.data?.name || '')
  const fileSize = computed(() => currentFile.value?.data?.fileSize || 0)
  const mimeType = computed(() => currentFile.value?.data?.mimeType || '')

  // 新增计算属性 - 文件浏览相关
  const hasFileList = computed(() => fileList.value.length > 0) // 是否有文件列表
  const hasSearchResults = computed(() => searchResults.value.length > 0) // 是否有搜索结果
  const isInSearchMode = computed(() => searchKeyword.value.trim() !== '') // 是否在搜索模式
  const hasNextPage = computed(() => currentPage.value < totalPages.value) // 是否有下一页
  const hasPrevPage = computed(() => currentPage.value > 1) // 是否有上一页
  const currentBreadcrumb = computed(() => breadcrumbItems.value) // 当前面包屑
  const isRootDirectory = computed(() => currentParentId.value === null) // 是否在根目录

  // 设置加载状态
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  // 设置错误状态
  const setError = (error: string) => {
    hasError.value = true
    errorMessage.value = error
  }

  // 清除错误状态
  const clearError = () => {
    hasError.value = false
    errorMessage.value = ''
  }

  // 清除当前文件
  const clearCurrentFile = () => {
    currentFile.value = null
    clearError()
  }

  // 新增方法 - 文件列表状态管理
  const setFileListLoading = (loading: boolean) => {
    isFileListLoading.value = loading
  }

  const setFileListError = (error: string) => {
    fileListError.value = error
  }

  const clearFileListError = () => {
    fileListError.value = ''
  }

  const setSearchLoading = (loading: boolean) => {
    isSearching.value = loading
  }

  const setSearchError = (error: string) => {
    searchError.value = error
  }

  const clearSearchError = () => {
    searchError.value = ''
  }

  // 清除文件浏览状态
  const clearFileListState = () => {
    fileList.value = []
    currentParentId.value = null
    breadcrumbItems.value = []
    clearFileListError()
    currentPage.value = 1
    totalFiles.value = 0
    totalPages.value = 0
  }

  // 清除搜索状态
  const clearSearchState = () => {
    searchResults.value = []
    searchKeyword.value = ''
    clearSearchError()
  }

  // 根据文件ID获取文件内容
  const getFileContent = async (fileId: number) => {
    setLoading(true)
    clearError()

    try {
      const response = await fileApi.getFileContent(fileId)
      currentFile.value = response.data
      return response.data.data
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '获取文件内容失败'
      setError(message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 根据文件ID获取文件信息
  const getFileInfo = async (fileId: number) => {
    try {
      const response = await fileApi.getFileInfo(fileId)
      return response.data
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '获取文件信息失败'
      setError(message)
      throw error
    }
  }

  // 新增方法 - 获取文件列表
  const getFileList = async (subjectId: number, params?: FileListParams) => {
    setFileListLoading(true)
    clearFileListError()

    try {
      // 更新当前状态
      currentSubjectId.value = subjectId
      if (params?.parentId !== undefined) {
        currentParentId.value = params.parentId
      }
      if (params?.page) {
        currentPage.value = params.page
      }

      const response = await fileApi.getFileList(subjectId, params)
      const data = response.data.data

      // 更新文件列表状态
      fileList.value = data.files
      totalFiles.value = data.pagination.total
      totalPages.value = data.pagination.totalPages
      currentPage.value = data.pagination.page

      return data
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '获取文件列表失败'
      setFileListError(message)
      throw error
    } finally {
      setFileListLoading(false)
    }
  }

  // 获取面包屑导航
  const getBreadcrumb = async (fileId: number) => {
    try {
      const response = await fileApi.getBreadcrumb(fileId)
      const data = response.data.data
      breadcrumbItems.value = data.breadcrumb
      return data
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '获取面包屑导航失败'
      setFileListError(message)
      throw error
    }
  }

  // 搜索文件
  const searchFiles = async (subjectId: number, params: FileSearchParams) => {
    setSearchLoading(true)
    clearSearchError()

    try {
      // 更新搜索关键词
      searchKeyword.value = params.q
      currentSubjectId.value = subjectId

      const response = await fileApi.searchFiles(subjectId, params)
      const data = response.data.data

      // 更新搜索结果
      searchResults.value = data.results

      return data
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '搜索文件失败'
      setSearchError(message)
      throw error
    } finally {
      setSearchLoading(false)
    }
  }

  // 导航到指定目录
  const navigateToDirectory = async (subjectId: number, parentId: number | null, page: number = 1) => {
    return await getFileList(subjectId, { parentId, page, limit: pageSize.value })
  }

  // 切换页码
  const changePage = async (page: number) => {
    if (!currentSubjectId.value) return

    const params: FileListParams = {
      parentId: currentParentId.value,
      page,
      limit: pageSize.value
    }

    return await getFileList(currentSubjectId.value, params)
  }

  return {
    // 原有状态
    currentFile: computed(() => currentFile.value),
    isLoading: computed(() => isLoading.value),
    hasError: computed(() => hasError.value),
    errorMessage: computed(() => errorMessage.value),

    // 新增状态 - 文件浏览
    fileList: computed(() => fileList.value),
    currentSubjectId: computed(() => currentSubjectId.value),
    currentParentId: computed(() => currentParentId.value),
    breadcrumbItems: computed(() => breadcrumbItems.value),
    isFileListLoading: computed(() => isFileListLoading.value),
    fileListError: computed(() => fileListError.value),

    // 新增状态 - 搜索
    searchResults: computed(() => searchResults.value),
    searchKeyword: computed(() => searchKeyword.value),
    isSearching: computed(() => isSearching.value),
    searchError: computed(() => searchError.value),

    // 新增状态 - 分页
    currentPage: computed(() => currentPage.value),
    pageSize: computed(() => pageSize.value),
    totalFiles: computed(() => totalFiles.value),
    totalPages: computed(() => totalPages.value),

    // 原有计算属性
    fileContent,
    fileName,
    fileSize,
    mimeType,

    // 新增计算属性
    hasFileList,
    hasSearchResults,
    isInSearchMode,
    hasNextPage,
    hasPrevPage,
    currentBreadcrumb,
    isRootDirectory,

    // 原有方法
    getFileContent,
    getFileInfo,
    clearCurrentFile,
    clearError,

    // 新增方法
    getFileList,
    getBreadcrumb,
    searchFiles,
    navigateToDirectory,
    changePage,
    clearFileListState,
    clearSearchState,
    setFileListLoading,
    setFileListError,
    clearFileListError,
    setSearchLoading,
    setSearchError,
    clearSearchError
  }
})
