const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// 调试multipart上传
test('调试multipart上传', async ({ request }) => {
    // 创建测试文件
    const testContent = '# 测试文件\n这是一个测试文件内容。';
    const testFilePath = path.join(__dirname, 'test_debug.md');
    fs.writeFileSync(testFilePath, testContent);

    try {
        // 读取文件内容
        const fileBuffer = fs.readFileSync(testFilePath);
        
        console.log('=== 测试信息 ===');
        console.log('文件大小:', fileBuffer.length);
        console.log('文件内容:', testContent);

        // 发送上传请求 - 使用Playwright官方推荐的Object格式
        console.log('\n=== 发送请求 ===');
        const response = await request.post('http://localhost:3001/api/subjects/1/upload', {
            multipart: {
                file: {
                    name: 'test_debug.md',
                    mimeType: 'text/markdown',
                    buffer: fileBuffer
                }
            }
        });

        console.log('响应状态:', response.status());
        const responseBody = await response.text();
        console.log('响应内容:', responseBody);

    } finally {
        // 清理测试文件
        if (fs.existsSync(testFilePath)) {
            fs.unlinkSync(testFilePath);
        }
    }
});
