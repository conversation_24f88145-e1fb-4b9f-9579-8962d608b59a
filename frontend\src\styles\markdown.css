/* Markdown内容样式 */

.markdown-content {
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.25;
  color: #1f2328;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 8px;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content h4 {
  font-size: 1em;
}

.markdown-content h5 {
  font-size: 0.875em;
}

.markdown-content h6 {
  font-size: 0.85em;
  color: #656d76;
}

/* 段落样式 */
.markdown-content p {
  margin: 16px 0;
  line-height: 1.6;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  margin: 16px 0;
  padding-left: 2em;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.6;
}

.markdown-content li > p {
  margin: 8px 0;
}

/* 引用样式 */
.markdown-content blockquote {
  margin: 16px 0;
  padding: 0 16px;
  color: #656d76;
  border-left: 4px solid #d1d9e0;
  background: #f6f8fa;
}

.markdown-content blockquote > :first-child {
  margin-top: 0;
}

.markdown-content blockquote > :last-child {
  margin-bottom: 0;
}

/* 代码样式 */
.markdown-content code {
  padding: 2px 4px;
  font-size: 85%;
  background: #f6f8fa;
  border-radius: 3px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

.markdown-content pre {
  margin: 16px 0;
  padding: 16px;
  overflow: auto;
  background: #f6f8fa;
  border-radius: 6px;
  line-height: 1.45;
}

.markdown-content pre code {
  padding: 0;
  background: transparent;
  border-radius: 0;
  font-size: 85%;
}

/* 代码高亮样式 */
.markdown-content .hljs {
  background: #f6f8fa !important;
  color: #24292f;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-content .hljs-comment,
.markdown-content .hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.markdown-content .hljs-keyword,
.markdown-content .hljs-selector-tag,
.markdown-content .hljs-subst {
  color: #d73a49;
}

.markdown-content .hljs-number,
.markdown-content .hljs-literal,
.markdown-content .hljs-variable,
.markdown-content .hljs-template-variable,
.markdown-content .hljs-tag .hljs-attr {
  color: #005cc5;
}

.markdown-content .hljs-string,
.markdown-content .hljs-doctag {
  color: #032f62;
}

.markdown-content .hljs-title,
.markdown-content .hljs-section,
.markdown-content .hljs-selector-id {
  color: #6f42c1;
  font-weight: bold;
}

/* 表格样式 */
.markdown-content table {
  width: 100%;
  margin: 16px 0;
  border-collapse: collapse;
  border-spacing: 0;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.markdown-content table thead {
  background: #f6f8fa;
}

.markdown-content table th,
.markdown-content table td {
  padding: 8px 12px;
  border: 1px solid #d1d9e0;
  text-align: left;
  vertical-align: top;
  white-space: normal;
}

.markdown-content table th {
  font-weight: 600;
  background: #f6f8fa;
}

.markdown-content table tr:nth-child(even) {
  background: #f6f8fa;
}

/* 链接样式 */
.markdown-content a {
  color: #0969da;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content a:visited {
  color: #8250df;
}

/* 分割线样式 */
.markdown-content hr {
  height: 1px;
  margin: 24px 0;
  background: #d1d9e0;
  border: 0;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 8px 0;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .markdown-content table {
    font-size: 12px;
  }
  
  .markdown-content table th,
  .markdown-content table td {
    padding: 6px 8px;
  }
}

/* 任务列表样式 */
.markdown-content .task-list-item {
  list-style-type: none;
  margin-left: -1.5em;
}

.markdown-content .task-list-item input[type="checkbox"] {
  margin-right: 8px;
}

/* 强调样式 */
.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content del {
  text-decoration: line-through;
  color: #656d76;
}

/* 键盘按键样式 */
.markdown-content kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: 1px solid #d1d5da;
  border-bottom-color: #c6cbd1;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #c6cbd1;
}

/* 脚注样式 */
.markdown-content .footnote-ref {
  font-size: 0.8em;
  vertical-align: super;
}

.markdown-content .footnotes {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #d1d9e0;
  font-size: 0.9em;
}

/* 打印样式 */
@media print {
  .markdown-content {
    color: #000;
    background: #fff;
  }
  
  .markdown-content a {
    color: #000;
    text-decoration: underline;
  }
  
  .markdown-content .hljs {
    background: #fff !important;
    color: #000;
    border: 1px solid #ccc;
  }
}
