<template>
  <div class="markdown-viewer">
    <a-spin :spinning="loading" size="large">
      <div
        class="markdown-content"
        v-if="content.trim()"
        v-html="renderedContent"
      />
      <div class="markdown-content empty-content" v-else>
        <p>暂无内容</p>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'


// 组件Props接口
interface MarkdownViewerProps {
  content: string
  loading?: boolean
  enableHtml?: boolean
  enableLinkify?: boolean
  enableTypographer?: boolean
}

// 组件Events接口
interface MarkdownViewerEmits {
  contentRendered: [html: string]
  renderError: [error: string]
}

const props = withDefaults(defineProps<MarkdownViewerProps>(), {
  loading: false,
  enableHtml: true,
  enableLinkify: true,
  enableTypographer: true
})

const emit = defineEmits<MarkdownViewerEmits>()

// Markdown渲染器配置
const md: MarkdownIt = new MarkdownIt({
  html: props.enableHtml,
  linkify: props.enableLinkify,
  typographer: props.enableTypographer,
  breaks: true, // 支持换行
  highlight: (str: string, lang: string) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        const highlighted = hljs.highlight(str, { language: lang }).value
        return `<pre class="hljs"><code class="hljs-${lang}">${highlighted}</code></pre>`
      } catch (error) {
        console.warn('代码高亮失败:', error)
      }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})

// 渲染内容
const renderedContent = computed(() => {
  if (!props.content) {
    return '<div class="empty-content">暂无内容</div>'
  }

  try {
    const html = md.render(props.content)
    emit('contentRendered', html)
    return html
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '渲染失败'
    emit('renderError', errorMessage)
    return `<div class="render-error">Markdown渲染失败: ${errorMessage}</div>`
  }
})

// 监听内容变化
watch(() => props.content, () => {
  // 内容变化时重新渲染
}, { immediate: true })
</script>

<style scoped>
.markdown-viewer {
  width: 100%;
  min-height: 200px;
}

.markdown-content {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

.empty-content {
  text-align: center;
  color: #999;
  padding: 40px 20px;
  font-size: 16px;
}

.render-error {
  color: #ff4d4f;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 13px;
    padding: 0 8px;
  }
}

@media (max-width: 480px) {
  .markdown-content {
    font-size: 12px;
    padding: 0 4px;
  }
  
  .empty-content {
    padding: 20px 10px;
    font-size: 14px;
  }
}
</style>
