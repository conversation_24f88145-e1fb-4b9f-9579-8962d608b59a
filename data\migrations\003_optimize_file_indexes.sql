-- 文件浏览功能索引优化脚本
-- 为file_nodes表添加复合索引以优化文件浏览查询性能
-- 迁移版本: 003
-- 创建时间: 2025-01-29
-- 目标: 支持Sprint 03基础文件浏览切片的高性能查询

-- 复合索引：学科ID + 父级ID (用于层级浏览)
-- 这个索引已经在002迁移中创建，这里确保存在
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);

-- 复合索引：学科ID + 文件名 (用于搜索功能)
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_name ON file_nodes(subject_id, name);

-- 复合索引：父级ID + 类型 + 文件名 (用于文件夹内容排序)
CREATE INDEX IF NOT EXISTS idx_file_nodes_parent_type_name ON file_nodes(parent_id, type, name);

-- 复合索引：学科ID + 类型 (用于按类型筛选)
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_type ON file_nodes(subject_id, type);

-- 复合索引：学科ID + 创建时间 (用于按时间排序)
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_created ON file_nodes(subject_id, created_at);

-- 复合索引：学科ID + 更新时间 (用于按更新时间排序)
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_updated ON file_nodes(subject_id, updated_at);

-- 文件名模糊搜索索引 (SQLite支持的文本搜索优化)
-- 注意：SQLite的LIKE查询在有索引的情况下性能会更好
CREATE INDEX IF NOT EXISTS idx_file_nodes_name_lower ON file_nodes(LOWER(name));

-- 文件路径索引 (用于路径相关查询)
CREATE INDEX IF NOT EXISTS idx_file_nodes_file_path ON file_nodes(file_path);

-- 验证索引创建
SELECT 'File browsing indexes created successfully' as message;

-- 显示所有索引信息
SELECT 
    name as index_name,
    tbl_name as table_name,
    sql as index_definition
FROM sqlite_master 
WHERE type = 'index' 
    AND tbl_name = 'file_nodes'
    AND name LIKE 'idx_%'
ORDER BY name;

-- 性能测试查询示例
-- 这些查询将在索引创建后执行，验证性能提升

-- 1. 层级浏览查询 (获取指定学科下的根级文件)
EXPLAIN QUERY PLAN 
SELECT id, name, type, file_size, created_at 
FROM file_nodes 
WHERE subject_id = 1 AND parent_id IS NULL 
ORDER BY type DESC, name ASC;

-- 2. 文件夹内容查询 (获取指定文件夹下的内容)
EXPLAIN QUERY PLAN 
SELECT id, name, type, file_size, created_at 
FROM file_nodes 
WHERE parent_id = 1 
ORDER BY type DESC, name ASC;

-- 3. 文件搜索查询 (在指定学科中搜索文件名)
EXPLAIN QUERY PLAN 
SELECT id, name, type, file_path, created_at 
FROM file_nodes 
WHERE subject_id = 1 AND LOWER(name) LIKE LOWER('%数学%') 
ORDER BY name ASC;

-- 4. 分页查询 (支持大量文件的分页显示)
EXPLAIN QUERY PLAN 
SELECT id, name, type, file_size, created_at 
FROM file_nodes 
WHERE subject_id = 1 AND parent_id IS NULL 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;

-- 统计信息
SELECT 
    'Total file nodes' as metric,
    COUNT(*) as value
FROM file_nodes
UNION ALL
SELECT 
    'Files count' as metric,
    COUNT(*) as value
FROM file_nodes 
WHERE type = 'file'
UNION ALL
SELECT 
    'Folders count' as metric,
    COUNT(*) as value
FROM file_nodes 
WHERE type = 'folder'
UNION ALL
SELECT 
    'Subjects with files' as metric,
    COUNT(DISTINCT subject_id) as value
FROM file_nodes;
