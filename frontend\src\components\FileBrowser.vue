<template>
  <div class="file-browser">
    <!-- 头部操作区 -->
    <div class="browser-header bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <!-- 标题和统计 -->
        <div class="flex items-center space-x-4">
          <div>
            <h2 class="text-xl font-bold text-gray-900">文件浏览器</h2>
            <p class="text-gray-600 text-sm mt-1">
              当前目录共 <span data-testid="file-count">{{ filteredFiles.length }}</span> 个项目
            </p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-3">
          <!-- 搜索框 -->
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索文件名..."
            style="width: 200px"
            data-testid="file-search-input"
            @search="handleSearch"
            @change="handleSearchChange"
            :loading="searchLoading"
          />
          
          <!-- 视图切换 -->
          <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
            <a-radio-button value="grid" data-testid="view-toggle-grid">
              <i class="i-carbon-grid text-sm"></i>
            </a-radio-button>
            <a-radio-button value="list" data-testid="view-toggle-list">
              <i class="i-carbon-list text-sm"></i>
            </a-radio-button>
          </a-radio-group>

          <!-- 类型过滤 -->
          <a-select
            v-model:value="typeFilter"
            style="width: 120px"
            placeholder="类型过滤"
            allowClear
            @change="handleTypeFilterChange"
          >
            <a-select-option value="file">文件</a-select-option>
            <a-select-option value="folder">文件夹</a-select-option>
          </a-select>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div v-if="breadcrumb.length > 0" class="breadcrumb-nav bg-white rounded-lg shadow-sm p-4 mb-6">
      <a-breadcrumb>
        <a-breadcrumb-item 
          v-for="(item, index) in breadcrumb" 
          :key="item.id || 'root'"
          class="cursor-pointer"
          @click="handleBreadcrumbClick(item, index)"
        >
          <i :class="getBreadcrumbIcon(item.type)" class="mr-1"></i>
          {{ item.name }}
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 内容区域 -->
    <div class="browser-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <a-spin size="large" />
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredFiles.length === 0" class="empty-state text-center py-12">
        <div class="mb-4">
          <i class="i-carbon-folder-open text-6xl text-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          {{ searchKeyword ? '未找到相关文件' : '文件夹为空' }}
        </h3>
        <p class="text-gray-600 mb-6">
          {{ searchKeyword ? '尝试使用其他关键词搜索' : '当前目录下没有文件或文件夹' }}
        </p>
      </div>

      <!-- 文件列表 -->
      <div v-else data-testid="file-list-container">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'">
          <!-- 虚拟滚动网格视图 -->
          <div
            v-if="enableVirtualScroll && virtualScrollData"
            ref="virtualScrollContainer"
            class="virtual-scroll-container"
            :style="{ height: typeof height === 'number' ? `${height}px` : height, overflow: 'auto' }"
          >
            <div
              class="virtual-scroll-wrapper"
              :style="{ height: `${virtualScrollData.totalHeight}px`, position: 'relative' }"
            >
              <!-- 上方占位符 -->
              <div :style="{ height: `${virtualScrollData.offsetTop}px` }"></div>

              <!-- 可见项目 -->
              <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <div
                  v-for="file in virtualScrollData.visibleItems"
                  :key="file.id"
                  class="file-item bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow"
                  @click="handleFileClick(file)"
                  @dblclick="handleFileDoubleClick(file)"
                >
                  <div class="text-center">
                    <i :class="getFileIcon(file)" class="text-3xl mb-2" :style="{ color: getFileColor(file) }"></i>
                    <div class="text-sm font-medium text-gray-900 truncate" :title="file.name">
                      {{ file.name }}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                      {{ formatFileInfo(file) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 下方占位符 -->
              <div :style="{ height: `${virtualScrollData.offsetBottom}px` }"></div>
            </div>
          </div>

          <!-- 普通网格视图 -->
          <div
            v-else
            class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4"
          >
            <div
              v-for="file in paginatedFiles"
              :key="file.id"
              class="file-item bg-white rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition-shadow"
              @click="handleFileClick(file)"
              @dblclick="handleFileDoubleClick(file)"
            >
              <div class="text-center">
                <i :class="getFileIcon(file)" class="text-3xl mb-2" :style="{ color: getFileColor(file) }"></i>
                <div class="text-sm font-medium text-gray-900 truncate" :title="file.name">
                  {{ file.name }}
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ formatFileInfo(file) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="bg-white rounded-lg shadow-sm">
          <a-table
            :columns="tableColumns"
            :data-source="paginatedFiles"
            :pagination="paginationConfig"
            row-key="id"
            @change="handleTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="flex items-center space-x-3 cursor-pointer" @click="handleFileClick(record)">
                  <i :class="getFileIcon(record)" :style="{ color: getFileColor(record) }"></i>
                  <div>
                    <div class="font-medium text-gray-900">{{ record.name }}</div>
                    <div class="text-sm text-gray-500">{{ record.type === 'folder' ? '文件夹' : getFileTypeLabel(record) }}</div>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'size'">
                {{ formatFileSize(record.file_size) }}
              </template>
              <template v-else-if="column.key === 'updated_at'">
                {{ formatDate(record.updated_at) }}
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { fileApi } from '@/api/file'
import { useVirtualScroll } from '@/composables/useVirtualScroll'
import type {
  FileNode,
  FileListParams,
  FileSearchParams,
  BreadcrumbItem,
  FileListResponse
} from '@/types'

// Props定义
interface Props {
  subjectId: number
  initialParentId?: number | null
  height?: string | number
  showBreadcrumb?: boolean
  enableSearch?: boolean
  enableVirtualScroll?: boolean
  virtualScrollItemHeight?: number
  virtualScrollVisibleCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  initialParentId: null,
  height: 'auto',
  showBreadcrumb: true,
  enableSearch: true,
  enableVirtualScroll: true,
  virtualScrollItemHeight: 120,
  virtualScrollVisibleCount: 20
})

// Emits定义
interface Emits {
  fileClick: [file: FileNode]
  fileDoubleClick: [file: FileNode]
  folderEnter: [folder: FileNode]
  breadcrumbClick: [item: BreadcrumbItem, index: number]
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const files = ref<FileNode[]>([])
const searchResults = ref<FileNode[]>([])
const breadcrumb = ref<BreadcrumbItem[]>([])
const searchKeyword = ref('')
const viewMode = ref<'grid' | 'list'>('grid')
const typeFilter = ref<'file' | 'folder' | undefined>(undefined)
const currentParentId = ref<number | null>(props.initialParentId)
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 虚拟滚动相关
const virtualScrollContainer = ref<HTMLElement | null>(null)

// 表格列定义
const tableColumns = [
  { title: '名称', key: 'name', dataIndex: 'name', width: '40%' },
  { title: '大小', key: 'size', dataIndex: 'file_size', width: '15%' },
  { title: '类型', key: 'type', dataIndex: 'type', width: '15%' },
  { title: '修改时间', key: 'updated_at', dataIndex: 'updated_at', width: '30%' }
]

// 计算属性
const filteredFiles = computed(() => {
  if (searchKeyword.value.trim()) {
    return searchResults.value || []
  }

  let result = files.value || []

  if (typeFilter.value) {
    result = result.filter(file => file.type === typeFilter.value)
  }

  return result
})

const paginatedFiles = computed(() => {
  if (viewMode.value === 'list') {
    return filteredFiles.value // 表格组件自己处理分页
  }

  // 如果启用虚拟滚动，返回所有文件（虚拟滚动会处理可见性）
  if (props.enableVirtualScroll && viewMode.value === 'grid') {
    return filteredFiles.value
  }

  // 网格视图的分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFiles.value.slice(start, end)
})

// 虚拟滚动
const virtualScrollData = computed(() => {
  if (props.enableVirtualScroll && viewMode.value === 'grid' && virtualScrollContainer.value && filteredFiles.value.length > 0) {
    const virtualScroll = useVirtualScroll(filteredFiles, virtualScrollContainer, {
      itemHeight: props.virtualScrollItemHeight!,
      visibleCount: props.virtualScrollVisibleCount,
      buffer: 5
    })

    return {
      totalHeight: virtualScroll.totalHeight.value,
      offsetTop: virtualScroll.offsetTop.value,
      offsetBottom: virtualScroll.offsetBottom.value,
      visibleItems: virtualScroll.visibleItems.value
    }
  }
  return null
})

const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: filteredFiles.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
}))

// 工具方法
const getFileIcon = (file: FileNode): string => {
  if (file.type === 'folder') {
    return 'i-carbon-folder'
  }

  const ext = file.name.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'md':
    case 'markdown':
      return 'i-carbon-document'
    case 'pdf':
      return 'i-carbon-document-pdf'
    case 'doc':
    case 'docx':
      return 'i-carbon-document-word-processor'
    case 'xls':
    case 'xlsx':
      return 'i-carbon-document-spreadsheet'
    case 'ppt':
    case 'pptx':
      return 'i-carbon-document-presentation'
    case 'txt':
      return 'i-carbon-document-text'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      return 'i-carbon-image'
    case 'mp4':
    case 'avi':
    case 'mov':
      return 'i-carbon-video'
    case 'mp3':
    case 'wav':
    case 'flac':
      return 'i-carbon-music'
    case 'zip':
    case 'rar':
    case '7z':
      return 'i-carbon-archive'
    default:
      return 'i-carbon-document-unknown'
  }
}

const getFileColor = (file: FileNode): string => {
  if (file.type === 'folder') {
    return '#1890ff'
  }

  const ext = file.name.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'md':
    case 'markdown':
      return '#52c41a'
    case 'pdf':
      return '#f5222d'
    case 'doc':
    case 'docx':
      return '#1890ff'
    case 'xls':
    case 'xlsx':
      return '#52c41a'
    case 'ppt':
    case 'pptx':
      return '#fa8c16'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp':
      return '#722ed1'
    case 'mp4':
    case 'avi':
    case 'mov':
      return '#eb2f96'
    case 'mp3':
    case 'wav':
    case 'flac':
      return '#13c2c2'
    default:
      return '#8c8c8c'
  }
}

const getBreadcrumbIcon = (type: string): string => {
  switch (type) {
    case 'subject':
      return 'i-carbon-book'
    case 'folder':
      return 'i-carbon-folder'
    case 'file':
      return 'i-carbon-document'
    default:
      return 'i-carbon-home'
  }
}

const getFileTypeLabel = (file: FileNode): string => {
  if (!file.mime_type) return '未知类型'

  if (file.mime_type.startsWith('text/')) return '文本文件'
  if (file.mime_type.startsWith('image/')) return '图片文件'
  if (file.mime_type.startsWith('video/')) return '视频文件'
  if (file.mime_type.startsWith('audio/')) return '音频文件'
  if (file.mime_type.includes('pdf')) return 'PDF文档'
  if (file.mime_type.includes('word')) return 'Word文档'
  if (file.mime_type.includes('excel') || file.mime_type.includes('spreadsheet')) return 'Excel表格'
  if (file.mime_type.includes('powerpoint') || file.mime_type.includes('presentation')) return 'PPT演示'

  return file.mime_type
}

const formatFileSize = (size?: number): string => {
  if (!size) return '-'

  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(1)} ${units[index]}`
}

const formatFileInfo = (file: FileNode): string => {
  if (file.type === 'folder') {
    return '文件夹'
  }
  return formatFileSize(file.file_size)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// API调用方法
const loadFiles = async (parentId: number | null = null) => {
  loading.value = true
  try {
    const params: FileListParams = {
      page: 1,
      limit: 100, // 后端限制最大100
      parentId: parentId || undefined
    }

    if (typeFilter.value) {
      params.type = typeFilter.value
    }

    const response = await fileApi.getFileList(props.subjectId, params)
    files.value = response.data.files || []
    total.value = response.data.pagination?.total || 0

    // 加载面包屑导航
    if (parentId && props.showBreadcrumb) {
      await loadBreadcrumb(parentId)
    } else if (!parentId) {
      breadcrumb.value = []
    }

  } catch (error: any) {
    console.error('加载文件列表失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '加载文件列表失败'
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const loadBreadcrumb = async (fileId: number) => {
  try {
    const response = await fileApi.getBreadcrumb(fileId)
    breadcrumb.value = response.data.breadcrumb
  } catch (error: any) {
    console.error('加载面包屑导航失败:', error)
    // 面包屑加载失败不影响主要功能，只记录错误
  }
}

const searchFiles = async (keyword: string) => {
  if (!keyword.trim()) {
    searchResults.value = []
    return
  }

  searchLoading.value = true
  try {
    const params: FileSearchParams = {
      q: keyword.trim(),
      limit: 100
    }

    if (typeFilter.value) {
      params.type = typeFilter.value
    }

    const response = await fileApi.searchFiles(props.subjectId, params)
    searchResults.value = response.data.results

  } catch (error: any) {
    console.error('搜索文件失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '搜索文件失败'
    message.error(errorMessage)
  } finally {
    searchLoading.value = false
  }
}

// 事件处理方法
const handleFileClick = (file: FileNode) => {
  emit('fileClick', file)
}

const handleFileDoubleClick = (file: FileNode) => {
  if (file.type === 'folder') {
    handleFolderEnter(file)
  }
  emit('fileDoubleClick', file)
}

const handleFolderEnter = (folder: FileNode) => {
  currentParentId.value = folder.id
  currentPage.value = 1
  loadFiles(folder.id)
  emit('folderEnter', folder)
}

const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
  // 点击面包屑导航到对应目录
  if (item.type === 'subject') {
    currentParentId.value = null
    loadFiles(null)
  } else if (item.id) {
    currentParentId.value = item.id
    loadFiles(item.id)
  }

  emit('breadcrumbClick', item, index)
}

const handleSearch = (value: string) => {
  searchKeyword.value = value
  if (value.trim()) {
    searchFiles(value)
  } else {
    searchResults.value = []
  }
}

const handleSearchChange = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  if (!value.trim()) {
    searchResults.value = []
  }
}

const handleTypeFilterChange = (value: 'file' | 'folder' | undefined) => {
  typeFilter.value = value
  currentPage.value = 1

  if (searchKeyword.value.trim()) {
    searchFiles(searchKeyword.value)
  } else {
    loadFiles(currentParentId.value)
  }
}

const handleTableChange = (pagination: any) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
}

// 公开方法
const refresh = () => {
  loadFiles(currentParentId.value)
}

const navigateToFolder = (folderId: number | null) => {
  currentParentId.value = folderId
  currentPage.value = 1
  loadFiles(folderId)
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
}

// 暴露给父组件的方法
defineExpose({
  refresh,
  navigateToFolder,
  clearSearch,
  loadFiles
})

// 监听器
watch(() => props.subjectId, (newSubjectId) => {
  if (newSubjectId) {
    currentParentId.value = props.initialParentId
    currentPage.value = 1
    loadFiles(props.initialParentId)
  }
})

watch(() => props.initialParentId, (newParentId) => {
  currentParentId.value = newParentId
  currentPage.value = 1
  loadFiles(newParentId)
})



// 生命周期
onMounted(() => {
  if (props.subjectId) {
    loadFiles(props.initialParentId)
  }
})
</script>

<style scoped>
.file-browser {
  @apply w-full;
}

.file-item {
  @apply transition-all duration-200;
}

.file-item:hover {
  @apply transform scale-105;
}

.breadcrumb-nav .ant-breadcrumb-link {
  @apply hover:text-blue-600 transition-colors;
}

.empty-state {
  @apply bg-white rounded-lg shadow-sm;
}

/* 虚拟滚动优化 */
.file-list-container {
  @apply overflow-auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .browser-header {
    @apply p-4;
  }

  .browser-header .flex {
    @apply flex-col space-y-4;
  }

  .file-item {
    @apply p-3;
  }
}
</style>
