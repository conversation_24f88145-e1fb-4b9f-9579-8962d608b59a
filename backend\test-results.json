🚀 开始全局测试设置...
✅ 测试环境准备完成
📡 启动测试服务器...
✅ 服务器已启动并响应正常
✅ 全局测试设置完成
🧹 开始全局测试清理...
🛑 关闭测试服务器...
✅ 测试环境清理完成
✅ 全局测试清理完成
{
  "config": {
    "configFile": "D:\\ai\\qimofuxi\\backend\\playwright.config.js",
    "rootDir": "D:/ai/qimofuxi/backend/tests",
    "forbidOnly": false,
    "fullyParallel": false,
    "globalSetup": "D:\\ai\\qimofuxi\\backend\\tests\\global-setup.js",
    "globalTeardown": "D:\\ai\\qimofuxi\\backend\\tests\\global-teardown.js",
    "globalTimeout": 0,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 0,
    "metadata": {
      "actualWorkers": 1
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "D:/ai/qimofuxi/backend/test-results",
        "repeatEach": 1,
        "retries": 2,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "API Tests",
        "name": "API Tests",
        "testDir": "D:/ai/qimofuxi/backend/tests/api",
        "testIgnore": [],
        "testMatch": [
          "**/*.test.js"
        ],
        "timeout": 30000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.54.1",
    "workers": 1,
    "webServer": null
  },
  "suites": [
    {
      "title": "api\\subjects.test.js",
      "file": "api/subjects.test.js",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "健康检查API测试",
          "file": "api/subjects.test.js",
          "line": 8,
          "column": 6,
          "specs": [
            {
              "title": "GET /health - 应该返回服务健康状态",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 46,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 健康检查响应时间: 25ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.241Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-a6aa2621e29448c5579a",
              "file": "api/subjects.test.js",
              "line": 9,
              "column": 5
            }
          ]
        },
        {
          "title": "学科管理API - 正常场景测试",
          "file": "api/subjects.test.js",
          "line": 33,
          "column": 6,
          "specs": [
            {
              "title": "GET /api/subjects - 应该返回学科列表",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 47,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 获取学科列表响应时间: 38ms, 学科数量: 138\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.296Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-08b9c222d1401953eecb",
              "file": "api/subjects.test.js",
              "line": 34,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 应该成功创建学科",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 37,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 创建学科响应时间: 26ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.346Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-4fd9e715aea9f7c6bfe5",
              "file": "api/subjects.test.js",
              "line": 63,
              "column": 5
            },
            {
              "title": "GET /api/subjects/:id - 应该返回指定学科详情",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 27,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 获取学科详情响应时间: 5ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.387Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-2e942bcab2e04d9f9d55",
              "file": "api/subjects.test.js",
              "line": 98,
              "column": 5
            }
          ]
        },
        {
          "title": "学科管理API - 边界条件测试",
          "file": "api/subjects.test.js",
          "line": 146,
          "column": 6,
          "specs": [
            {
              "title": "POST /api/subjects - 空名称应该返回验证错误",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 7,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.416Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-3793dcf7bbb9ca29bfe9",
              "file": "api/subjects.test.js",
              "line": 147,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 超长名称应该返回验证错误",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 6,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.426Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-0cb1e4a9eab0c5539888",
              "file": "api/subjects.test.js",
              "line": 162,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 重复名称应该返回错误",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 16,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:21.444Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:21.444Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 203
                        },
                        "snippet": "\u001b[0m \u001b[90m 201 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m secondResponse\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 203 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NAME_EXISTS'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 204 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科名称已存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 205 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 206 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 203
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:21.444Z\"}\u001b[39m\n\n  201 |         const data = await secondResponse.json();\n  202 |         expect(data).toHaveProperty('success', false);\n> 203 |         expect(data).toHaveProperty('code', 'SUBJECT_NAME_EXISTS');\n      |                      ^\n  204 |         expect(data.message).toContain('学科名称已存在');\n  205 |     });\n  206 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:21.434Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 203
                      }
                    },
                    {
                      "workerIndex": 1,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 42,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:23.281Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:23.281Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 203
                        },
                        "snippet": "\u001b[0m \u001b[90m 201 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m secondResponse\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 203 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NAME_EXISTS'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 204 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科名称已存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 205 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 206 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 203
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:23.281Z\"}\u001b[39m\n\n  201 |         const data = await secondResponse.json();\n  202 |         expect(data).toHaveProperty('success', false);\n> 203 |         expect(data).toHaveProperty('code', 'SUBJECT_NAME_EXISTS');\n      |                      ^\n  204 |         expect(data.message).toContain('学科名称已存在');\n  205 |     });\n  206 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-07-31T01:48:23.242Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 203
                      }
                    },
                    {
                      "workerIndex": 2,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 42,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:25.132Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:25.132Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 203
                        },
                        "snippet": "\u001b[0m \u001b[90m 201 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m secondResponse\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 202 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 203 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NAME_EXISTS'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 204 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科名称已存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 205 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 206 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 203
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NAME_EXISTS\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NAME_EXISTS\", \"message\": \"学科名称已存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:25.132Z\"}\u001b[39m\n\n  201 |         const data = await secondResponse.json();\n  202 |         expect(data).toHaveProperty('success', false);\n> 203 |         expect(data).toHaveProperty('code', 'SUBJECT_NAME_EXISTS');\n      |                      ^\n  204 |         expect(data.message).toContain('学科名称已存在');\n  205 |     });\n  206 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:203:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 2,
                      "startTime": "2025-07-31T01:48:25.095Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 203
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "d5753319561985ad6e63-eebe098234c2a75aba7c",
              "file": "api/subjects.test.js",
              "line": 179,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 特殊字符应该被正确处理",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 32,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:26.990Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-a745e07b150e98d51c07",
              "file": "api/subjects.test.js",
              "line": 207,
              "column": 5
            }
          ]
        },
        {
          "title": "学科管理API - 错误场景测试",
          "file": "api/subjects.test.js",
          "line": 228,
          "column": 6,
          "specs": [
            {
              "title": "GET /api/subjects/999999 - 不存在的学科ID应该返回404",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 14,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:27.043Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:27.043Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 236
                        },
                        "snippet": "\u001b[0m \u001b[90m 234 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 236 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NOT_FOUND'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 237 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科不存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 238 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 239 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 236
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:27.043Z\"}\u001b[39m\n\n  234 |         const data = await response.json();\n  235 |         expect(data).toHaveProperty('success', false);\n> 236 |         expect(data).toHaveProperty('code', 'SUBJECT_NOT_FOUND');\n      |                      ^\n  237 |         expect(data.message).toContain('学科不存在');\n  238 |     });\n  239 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:27.029Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 236
                      }
                    },
                    {
                      "workerIndex": 4,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 36,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:28.992Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:28.992Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 236
                        },
                        "snippet": "\u001b[0m \u001b[90m 234 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 236 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NOT_FOUND'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 237 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科不存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 238 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 239 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 236
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:28.992Z\"}\u001b[39m\n\n  234 |         const data = await response.json();\n  235 |         expect(data).toHaveProperty('success', false);\n> 236 |         expect(data).toHaveProperty('code', 'SUBJECT_NOT_FOUND');\n      |                      ^\n  237 |         expect(data.message).toContain('学科不存在');\n  238 |     });\n  239 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 1,
                      "startTime": "2025-07-31T01:48:28.962Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 236
                      }
                    },
                    {
                      "workerIndex": 5,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 63,
                      "error": {
                        "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:30.948Z\"}\u001b[39m",
                        "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:30.948Z\"}\u001b[39m\n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22",
                        "location": {
                          "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                          "column": 22,
                          "line": 236
                        },
                        "snippet": "\u001b[0m \u001b[90m 234 |\u001b[39m         \u001b[36mconst\u001b[39m data \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m response\u001b[33m.\u001b[39mjson()\u001b[33m;\u001b[39m\n \u001b[90m 235 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'success'\u001b[39m\u001b[33m,\u001b[39m \u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 236 |\u001b[39m         expect(data)\u001b[33m.\u001b[39mtoHaveProperty(\u001b[32m'code'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'SUBJECT_NOT_FOUND'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 237 |\u001b[39m         expect(data\u001b[33m.\u001b[39mmessage)\u001b[33m.\u001b[39mtoContain(\u001b[32m'学科不存在'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 238 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 239 |\u001b[39m     \u001b[0m"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                            "column": 22,
                            "line": 236
                          },
                          "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoHaveProperty\u001b[2m(\u001b[22m\u001b[32mpath\u001b[39m\u001b[2m, \u001b[22m\u001b[32mvalue\u001b[39m\u001b[2m)\u001b[22m\n\nExpected path: \u001b[32m\"code\"\u001b[39m\nReceived path: \u001b[31m[]\u001b[39m\n\nExpected value: \u001b[32m\"SUBJECT_NOT_FOUND\"\u001b[39m\nReceived value: \u001b[31m{\"error\": \"SUBJECT_NOT_FOUND\", \"message\": \"学科不存在\", \"requestId\": \"unknown\", \"success\": false, \"timestamp\": \"2025-07-31T01:48:30.948Z\"}\u001b[39m\n\n  234 |         const data = await response.json();\n  235 |         expect(data).toHaveProperty('success', false);\n> 236 |         expect(data).toHaveProperty('code', 'SUBJECT_NOT_FOUND');\n      |                      ^\n  237 |         expect(data.message).toContain('学科不存在');\n  238 |     });\n  239 |     \n    at D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js:236:22"
                        }
                      ],
                      "stdout": [],
                      "stderr": [],
                      "retry": 2,
                      "startTime": "2025-07-31T01:48:30.890Z",
                      "annotations": [],
                      "attachments": [],
                      "errorLocation": {
                        "file": "D:\\ai\\qimofuxi\\backend\\tests\\api\\subjects.test.js",
                        "column": 22,
                        "line": 236
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "d5753319561985ad6e63-2257c50ed77b97127c41",
              "file": "api/subjects.test.js",
              "line": 229,
              "column": 5
            },
            {
              "title": "GET /api/subjects/invalid - 无效的学科ID应该返回400",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 32,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:32.687Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-915bd0749f4867d24ef4",
              "file": "api/subjects.test.js",
              "line": 240,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 无效的JSON应该返回400",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 9,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:32.729Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-b08f004f7229d0d35618",
              "file": "api/subjects.test.js",
              "line": 249,
              "column": 5
            },
            {
              "title": "POST /api/subjects - 缺少Content-Type应该被正确处理",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 6,
                      "errors": [],
                      "stdout": [],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:32.741Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-6fd90b26b9b2900b87b4",
              "file": "api/subjects.test.js",
              "line": 257,
              "column": 5
            }
          ]
        },
        {
          "title": "学科管理API - 性能测试",
          "file": "api/subjects.test.js",
          "line": 274,
          "column": 6,
          "specs": [
            {
              "title": "批量创建学科性能测试",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 45,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 批量创建10个学科总时间: 36ms, 平均时间: 3.6ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:32.749Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-b7d2277b446efb621a84",
              "file": "api/subjects.test.js",
              "line": 275,
              "column": 5
            },
            {
              "title": "获取学科列表性能测试",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 30000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "API Tests",
                  "projectName": "API Tests",
                  "results": [
                    {
                      "workerIndex": 6,
                      "parallelIndex": 0,
                      "status": "passed",
                      "duration": 107,
                      "errors": [],
                      "stdout": [
                        {
                          "text": "✅ 20次获取列表 - 平均时间: 4.90ms, 最大时间: 10ms\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-07-31T01:48:32.797Z",
                      "annotations": [],
                      "attachments": []
                    }
                  ],
                  "status": "expected"
                }
              ],
              "id": "d5753319561985ad6e63-76e6f0a6ff48a03c33e8",
              "file": "api/subjects.test.js",
              "line": 307,
              "column": 5
            }
          ]
        }
      ]
    }
  ],
  "errors": [],
  "stats": {
    "startTime": "2025-07-31T01:48:18.348Z",
    "duration": 14606.275,
    "expected": 12,
    "skipped": 0,
    "unexpected": 2,
    "flaky": 0
  }
}
