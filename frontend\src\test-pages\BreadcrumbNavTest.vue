<template>
  <div class="breadcrumb-test p-6">
    <h1 class="text-2xl font-bold mb-6">BreadcrumbNav组件测试</h1>
    
    <!-- 测试控制面板 -->
    <div class="test-controls bg-gray-100 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-semibold mb-4">测试控制</h2>
      
      <!-- 基础配置 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <label class="block text-sm font-medium mb-1">最大显示项数:</label>
          <a-input-number v-model:value="maxItems" :min="2" :max="10" style="width: 100%" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">分隔符:</label>
          <a-select v-model:value="separator" style="width: 100%">
            <a-select-option value="/">/</a-select-option>
            <a-select-option value=">">></a-select-option>
            <a-select-option value="→">→</a-select-option>
            <a-select-option value="|">|</a-select-option>
          </a-select>
        </div>
        <div>
          <label class="block text-sm font-medium mb-1">主题:</label>
          <a-select v-model:value="theme" style="width: 100%">
            <a-select-option value="light">浅色</a-select-option>
            <a-select-option value="dark">深色</a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 开关配置 -->
      <div class="flex flex-wrap gap-4 mb-4">
        <a-switch v-model:checked="loading" checked-children="加载中" un-checked-children="正常" />
        <a-switch v-model:checked="showIcons" checked-children="显示图标" un-checked-children="隐藏图标" />
        <a-switch v-model:checked="clickable" checked-children="可点击" un-checked-children="不可点击" />
        <a-switch v-model:checked="responsive" checked-children="响应式" un-checked-children="固定" />
      </div>
      
      <!-- 路径操作 -->
      <div class="flex flex-wrap gap-2">
        <a-button @click="addPathItem" type="primary" size="small">添加路径项</a-button>
        <a-button @click="removeLastItem" size="small">删除最后项</a-button>
        <a-button @click="clearPath" size="small">清空路径</a-button>
        <a-button @click="loadSamplePath" size="small">加载示例路径</a-button>
        <a-button @click="loadLongPath" size="small">加载长路径</a-button>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log bg-white border rounded-lg p-4 mb-6">
      <h3 class="text-md font-semibold mb-2">事件日志</h3>
      <div class="max-h-32 overflow-y-auto">
        <div v-for="(log, index) in eventLogs" :key="index" class="text-sm py-1 border-b">
          <span class="text-gray-500">{{ log.time }}</span> - 
          <span class="font-medium">{{ log.event }}</span>: 
          <span class="text-blue-600">{{ log.data }}</span>
        </div>
        <div v-if="eventLogs.length === 0" class="text-gray-500 text-sm">暂无事件</div>
      </div>
      <a-button size="small" @click="clearLogs" class="mt-2">清空日志</a-button>
    </div>

    <!-- BreadcrumbNav组件测试区域 -->
    <div class="test-area">
      <h3 class="text-lg font-semibold mb-4">组件展示</h3>
      
      <!-- 不同尺寸测试 -->
      <div class="size-tests mb-6">
        <h4 class="text-md font-medium mb-2">尺寸测试</h4>
        <div class="space-y-4">
          <div>
            <label class="text-sm text-gray-600">小尺寸:</label>
            <BreadcrumbNav
              :items="breadcrumbItems"
              :max-items="maxItems"
              :separator="separator"
              :loading="loading"
              :show-icons="showIcons"
              :clickable="clickable"
              :theme="theme"
              :responsive="responsive"
              size="small"
              @item-click="handleItemClick"
              @back-click="handleBackClick"
              @path-change="handlePathChange"
            />
          </div>
          
          <div>
            <label class="text-sm text-gray-600">中等尺寸:</label>
            <BreadcrumbNav
              ref="breadcrumbRef"
              :items="breadcrumbItems"
              :max-items="maxItems"
              :separator="separator"
              :loading="loading"
              :show-icons="showIcons"
              :clickable="clickable"
              :theme="theme"
              :responsive="responsive"
              size="medium"
              @item-click="handleItemClick"
              @back-click="handleBackClick"
              @path-change="handlePathChange"
            />
          </div>
          
          <div>
            <label class="text-sm text-gray-600">大尺寸:</label>
            <BreadcrumbNav
              :items="breadcrumbItems"
              :max-items="maxItems"
              :separator="separator"
              :loading="loading"
              :show-icons="showIcons"
              :clickable="clickable"
              :theme="theme"
              :responsive="responsive"
              size="large"
              @item-click="handleItemClick"
              @back-click="handleBackClick"
              @path-change="handlePathChange"
            />
          </div>
        </div>
      </div>
      
      <!-- 空状态测试 -->
      <div class="empty-state-test mb-6">
        <h4 class="text-md font-medium mb-2">空状态测试</h4>
        <BreadcrumbNav
          :items="[]"
          :max-items="maxItems"
          :separator="separator"
          :loading="loading"
          :show-icons="showIcons"
          :clickable="clickable"
          :theme="theme"
          :responsive="responsive"
          @item-click="handleItemClick"
          @back-click="handleBackClick"
          @path-change="handlePathChange"
        />
      </div>
    </div>

    <!-- 测试按钮 -->
    <div class="test-actions mt-6 space-x-4">
      <a-button @click="testRefresh">测试刷新</a-button>
      <a-button @click="testExpandAll">展开所有</a-button>
      <a-button @click="testCollapseAll">折叠所有</a-button>
      <a-button @click="testMobileView">模拟移动端</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BreadcrumbNav from '@/components/BreadcrumbNav.vue'
import type { BreadcrumbItem } from '@/types'

// 测试参数
const maxItems = ref(5)
const separator = ref('/')
const theme = ref<'light' | 'dark'>('light')
const loading = ref(false)
const showIcons = ref(true)
const clickable = ref(true)
const responsive = ref(true)

// 组件引用
const breadcrumbRef = ref<InstanceType<typeof BreadcrumbNav>>()

// 面包屑数据
const breadcrumbItems = ref<BreadcrumbItem[]>([
  { id: null, name: '期末复习平台', type: 'subject', level: 0 },
  { id: 1, name: '数学', type: 'folder', level: 1 },
  { id: 2, name: '高等数学', type: 'folder', level: 2 }
])

// 事件日志
interface EventLog {
  time: string
  event: string
  data: string
}

const eventLogs = ref<EventLog[]>([])

const addLog = (event: string, data: any) => {
  const time = new Date().toLocaleTimeString()
  const logData = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data)
  eventLogs.value.unshift({
    time,
    event,
    data: logData
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 事件处理
const handleItemClick = (item: BreadcrumbItem, index: number) => {
  addLog('项目点击', `${item.name} (Index: ${index}, Type: ${item.type})`)
}

const handleBackClick = (previousItem: BreadcrumbItem) => {
  addLog('返回点击', `返回到: ${previousItem.name} (Type: ${previousItem.type})`)
}

const handlePathChange = (newPath: BreadcrumbItem[]) => {
  addLog('路径变化', `新路径长度: ${newPath.length}`)
}

// 路径操作方法
const addPathItem = () => {
  const newId = breadcrumbItems.value.length + 1
  const newItem: BreadcrumbItem = {
    id: newId,
    name: `文件夹${newId}`,
    type: 'folder',
    level: breadcrumbItems.value.length
  }
  breadcrumbItems.value.push(newItem)
  addLog('添加路径项', `添加: ${newItem.name}`)
}

const removeLastItem = () => {
  if (breadcrumbItems.value.length > 1) {
    const removed = breadcrumbItems.value.pop()
    addLog('删除路径项', `删除: ${removed?.name}`)
  }
}

const clearPath = () => {
  breadcrumbItems.value = []
  addLog('清空路径', '所有路径项已清空')
}

const loadSamplePath = () => {
  breadcrumbItems.value = [
    { id: null, name: '期末复习平台', type: 'subject', level: 0 },
    { id: 1, name: '数学', type: 'folder', level: 1 },
    { id: 2, name: '高等数学', type: 'folder', level: 2 },
    { id: 3, name: '第一章', type: 'folder', level: 3 }
  ]
  addLog('加载示例路径', '已加载4级路径')
}

const loadLongPath = () => {
  breadcrumbItems.value = [
    { id: null, name: '期末复习平台', type: 'subject', level: 0 },
    { id: 1, name: '计算机科学', type: 'folder', level: 1 },
    { id: 2, name: '数据结构与算法', type: 'folder', level: 2 },
    { id: 3, name: '第三章-树和图', type: 'folder', level: 3 },
    { id: 4, name: '3.2-二叉树', type: 'folder', level: 4 },
    { id: 5, name: '练习题', type: 'folder', level: 5 },
    { id: 6, name: '困难题目', type: 'folder', level: 6 },
    { id: 7, name: '题目集合A', type: 'folder', level: 7 }
  ]
  addLog('加载长路径', '已加载8级路径')
}

// 测试方法
const testRefresh = () => {
  if (breadcrumbRef.value) {
    breadcrumbRef.value.refresh()
    addLog('测试刷新', '调用refresh方法')
  }
}

const testExpandAll = () => {
  if (breadcrumbRef.value) {
    breadcrumbRef.value.expandAll()
    addLog('测试展开', '调用expandAll方法')
  }
}

const testCollapseAll = () => {
  if (breadcrumbRef.value) {
    breadcrumbRef.value.collapseAll()
    addLog('测试折叠', '调用collapseAll方法')
  }
}

const testMobileView = () => {
  // 模拟移动端视口
  const viewport = window.innerWidth < 768 ? '1200px' : '600px'
  addLog('模拟视口', `切换到${viewport}宽度`)
  
  // 触发resize事件
  window.dispatchEvent(new Event('resize'))
}

const clearLogs = () => {
  eventLogs.value = []
}

// 生命周期
onMounted(() => {
  addLog('组件挂载', 'BreadcrumbNav测试页面已加载')
})
</script>

<style scoped>
.breadcrumb-test {
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  border: 1px solid #d9d9d9;
}

.event-log {
  font-family: 'Courier New', monospace;
}

.size-tests > div {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}

.empty-state-test {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
}
</style>
