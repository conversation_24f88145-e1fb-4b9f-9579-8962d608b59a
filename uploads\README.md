# 文件上传存储目录

此目录用于存储用户上传的Markdown文件。

## 目录结构

```
uploads/
├── temp/           # 临时文件存储目录
├── {subjectId}/    # 按学科ID分组的文件存储目录
│   ├── file1.md
│   ├── file2.md
│   └── ...
└── README.md       # 本说明文件
```

## 存储规则

1. **按学科分组**: 每个学科的文件存储在独立的子目录中，目录名为学科ID
2. **文件命名**: 保持原始文件名，添加时间戳避免冲突
3. **文件类型**: 仅支持Markdown文件 (.md, .markdown)
4. **文件大小**: 单个文件最大10MB
5. **编码格式**: 统一使用UTF-8编码

## 安全考虑

- 文件类型严格验证，只允许Markdown文件
- 文件大小限制，防止存储空间滥用
- 路径安全检查，防止目录遍历攻击
- 文件名安全处理，避免特殊字符问题

## 维护说明

- 定期清理temp目录中的临时文件
- 监控存储空间使用情况
- 备份重要文件数据
