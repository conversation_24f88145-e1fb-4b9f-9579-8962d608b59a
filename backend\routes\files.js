// 文件管理路由
// 定义文件上传、下载、管理相关的API端点

const express = require('express');
const router = express.Router();

// 导入multer配置
const { upload } = require('../config/multer');

// 导入服务和中间件
const fileService = require('../services/fileService');
const {
    validateSubjectId,
    validateFileId,
    validateFileUpload
} = require('../middleware/fileValidation');
const { handleValidationErrors } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const { param } = require('express-validator');

/**
 * @route   POST /api/subjects/:id/upload
 * @desc    上传Markdown文件到指定学科
 * @access  Public
 * @param   {number} id - 学科ID
 * @param   {File} file - 上传的Markdown文件
 * @returns {Object} 上传结果和文件信息
 */
router.post('/subjects/:id/upload',
    validateSubjectId,
    handleValidationErrors,
    upload.single('file'),
    validateFileUpload,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const subjectId = req.params.id;
        const file = req.file;

        console.log(`📤 接收文件上传请求: 学科ID ${subjectId}, 文件 ${file.originalname}`);

        try {
            const result = await fileService.uploadFile(subjectId, file);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件上传API响应: ${responseTime}ms`);

            res.status(201).json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 文件上传API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'UPLOAD_ERROR';

            if (error.code === 'SUBJECT_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

/**
 * @route   GET /api/files/:fileId
 * @desc    根据文件ID获取文件信息和内容
 * @access  Public
 * @param   {number} fileId - 文件ID
 * @returns {Object} 文件信息和内容
 */
router.get('/files/:fileId',
    validateFileId,
    handleValidationErrors,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const fileId = req.params.fileId;

        console.log(`📄 接收文件获取请求: 文件ID ${fileId}`);

        try {
            const result = await fileService.getFileById(fileId);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件获取API响应: ${responseTime}ms`);

            res.json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 文件获取API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'GET_FILE_ERROR';

            if (error.code === 'FILE_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

/**
 * @route   GET /api/files/:fileId/content
 * @desc    根据文件ID获取文件内容（专用于内容读取）
 * @access  Public
 * @param   {number} fileId - 文件ID
 * @returns {Object} 文件内容和基本信息
 */
router.get('/files/:fileId/content',
    validateFileId,
    handleValidationErrors,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const fileId = req.params.fileId;

        console.log(`📖 接收文件内容获取请求: 文件ID ${fileId}`);

        try {
            const result = await fileService.getFileContent(fileId);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件内容获取API响应: ${responseTime}ms`);

            res.json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 文件内容获取API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'GET_FILE_CONTENT_ERROR';

            if (error.code === 'FILE_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

/**
 * @route   GET /api/subjects/:id/files
 * @desc    获取指定学科的文件列表，支持分页、层级浏览、搜索过滤
 * @access  Public
 * @param   {number} id - 学科ID
 * @query   {number} [parent_id] - 父目录ID，null表示根目录
 * @query   {number} [page=1] - 页码
 * @query   {number} [limit=50] - 每页数量
 * @query   {string} [search] - 搜索关键词
 * @query   {string} [type] - 文件类型过滤 ('file' | 'folder')
 * @returns {Object} 文件列表和分页信息
 */
router.get('/subjects/:id/files',
    validateSubjectId,
    handleValidationErrors,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const subjectId = req.params.id;

        // 解析查询参数
        const {
            parent_id,
            page = 1,
            limit = 50,
            search,
            type
        } = req.query;

        // 参数验证
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const parentId = parent_id ? parseInt(parent_id) : null;

        if (isNaN(pageNum) || pageNum < 1) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_PAGE',
                message: '页码必须是大于0的整数',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_LIMIT',
                message: '每页数量必须是1-100之间的整数',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        if (parent_id && isNaN(parentId)) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_PARENT_ID',
                message: '父目录ID必须是有效的整数',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        if (type && !['file', 'folder'].includes(type)) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_TYPE',
                message: '文件类型必须是 file 或 folder',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        console.log(`📋 接收文件列表请求: 学科ID ${subjectId}, 父目录 ${parentId}, 页码 ${pageNum}, 搜索 "${search || '无'}"`);

        try {
            const options = {
                parentId,
                page: pageNum,
                limit: limitNum,
                search: search?.trim() || null,
                type: type || null
            };

            const result = await fileService.getFilesBySubject(subjectId, options);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件列表API响应: ${responseTime}ms`);

            res.json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 文件列表API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'GET_FILES_ERROR';

            if (error.code === 'SUBJECT_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

/**
 * @route   GET /api/files/:id/breadcrumb
 * @desc    获取指定文件或文件夹的面包屑导航路径
 * @access  Public
 * @param   {number} id - 文件或文件夹ID
 * @returns {Object} 面包屑导航路径
 */
router.get('/files/:id/breadcrumb',
    param('id').isInt({ min: 1 }).withMessage('节点ID必须是大于0的整数').toInt(),
    handleValidationErrors,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const nodeId = req.params.id;

        console.log(`🧭 接收面包屑导航请求: 节点ID ${nodeId}`);

        try {
            const result = await fileService.getBreadcrumb(nodeId);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 面包屑导航API响应: ${responseTime}ms`);

            res.json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 面包屑导航API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'GET_BREADCRUMB_ERROR';

            if (error.code === 'NODE_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

/**
 * @route   GET /api/subjects/:id/search
 * @desc    在指定学科内搜索文件和文件夹
 * @access  Public
 * @param   {number} id - 学科ID
 * @query   {string} q - 搜索关键词（必填，至少2个字符）
 * @query   {string} [type] - 文件类型过滤 ('file' | 'folder')
 * @query   {number} [limit=20] - 结果数量限制
 * @query   {boolean} [include_content=false] - 是否在文件内容中搜索
 * @returns {Object} 搜索结果列表
 */
router.get('/subjects/:id/search',
    validateSubjectId,
    handleValidationErrors,
    asyncHandler(async (req, res) => {
        const startTime = Date.now();
        const subjectId = req.params.id;

        // 解析查询参数
        const {
            q: keyword,
            type,
            limit = 20,
            include_content = false
        } = req.query;

        // 参数验证
        if (!keyword || keyword.trim().length < 2) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_SEARCH_KEYWORD',
                message: '搜索关键词至少需要2个字符',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        const limitNum = parseInt(limit);
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_LIMIT',
                message: '结果数量限制必须是1-100之间的整数',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        if (type && !['file', 'folder'].includes(type)) {
            return res.status(400).json({
                success: false,
                error: 'INVALID_TYPE',
                message: '文件类型必须是 file 或 folder',
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }

        const includeContent = include_content === 'true' || include_content === true;

        console.log(`🔍 接收文件搜索请求: 学科ID ${subjectId}, 关键词 "${keyword.trim()}", 类型 ${type || '全部'}`);

        try {
            const options = {
                type: type || null,
                limit: limitNum,
                includeContent
            };

            const result = await fileService.searchFiles(subjectId, keyword.trim(), options);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件搜索API响应: ${responseTime}ms`);

            res.json({
                ...result,
                requestId: req.headers['x-request-id'] || 'unknown',
                responseTime: `${responseTime}ms`
            });

        } catch (error) {
            console.error(`❌ 文件搜索API错误:`, error.message);

            // 根据错误类型返回相应的HTTP状态码
            let statusCode = 500;
            let errorCode = 'SEARCH_FILES_ERROR';

            if (error.code === 'SUBJECT_NOT_FOUND') {
                statusCode = 404;
                errorCode = error.code;
            } else if (error.code === 'INVALID_SEARCH_KEYWORD') {
                statusCode = 400;
                errorCode = error.code;
            } else if (error.code === 'DATABASE_ERROR') {
                statusCode = 500;
                errorCode = error.code;
            }

            res.status(statusCode).json({
                success: false,
                error: errorCode,
                message: error.message,
                timestamp: new Date().toISOString(),
                requestId: req.headers['x-request-id'] || 'unknown'
            });
        }
    })
);

// 错误处理中间件 - 专门处理multer错误
router.use((error, req, res, next) => {
    console.error('🚨 文件路由错误:', error.message);

    // 处理multer特定错误
    if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
            success: false,
            error: 'FILE_TOO_LARGE',
            message: '文件大小超过限制（最大10MB）',
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] || 'unknown'
        });
    }

    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return res.status(400).json({
            success: false,
            error: 'UNEXPECTED_FILE',
            message: '上传了意外的文件字段',
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] || 'unknown'
        });
    }

    if (error.message && error.message.includes('只支持Markdown文件上传')) {
        return res.status(400).json({
            success: false,
            error: 'INVALID_FILE_TYPE',
            message: error.message,
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] || 'unknown'
        });
    }

    // 传递给全局错误处理器
    next(error);
});

module.exports = router;
