/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    BreadcrumbNav: typeof import('./src/components/BreadcrumbNav.vue')['default']
    CreateSubjectModal: typeof import('./src/components/CreateSubjectModal.vue')['default']
    FileBrowser: typeof import('./src/components/FileBrowser.vue')['default']
    FileSearch: typeof import('./src/components/FileSearch.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    MarkdownViewer: typeof import('./src/components/MarkdownViewer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SubjectCard: typeof import('./src/components/SubjectCard.vue')['default']
    SubjectManager: typeof import('./src/components/SubjectManager.vue')['default']
  }
}
