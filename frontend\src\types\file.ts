// 文件相关类型定义

export interface FileNode {
  id: number
  subject_id: number
  parent_id: number | null
  name: string
  type: 'file' | 'folder'
  content?: string
  file_path?: string
  file_size?: number
  mime_type?: string
  created_at: string
  updated_at: string
}

export interface UploadFileRequest {
  subjectId: number
  file: File
}

export interface UploadFileResponse {
  success: boolean
  data: {
    id: number
    name: string
    originalName: string
    size: number
    mimeType: string
    subjectId: number
    uploadTime: string
  }
  message: string
  timestamp: string
  requestId: string
  responseTime: string
}

export interface FileContentResponse {
  success: boolean
  data: {
    id: number
    name: string
    content: string
    type: string
    filePath: string
    fileSize: number
    mimeType: string
    createdAt: string
    updatedAt: string
  }
  message: string
  timestamp: string
}

// 文件上传状态
export interface UploadStatus {
  status: 'idle' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string | undefined
}

// 文件上传组件Props
export interface FileUploaderProps {
  subjectId: number
  accept?: string
  maxSize?: number
  disabled?: boolean
}

// 文件上传组件Emits
export interface FileUploaderEmits {
  uploadStart: []
  uploadProgress: [progress: number]
  uploadSuccess: [file: FileNode]
  uploadError: [error: string]
}

// Markdown渲染组件Props
export interface MarkdownViewerProps {
  content: string
  loading?: boolean
  enableHtml?: boolean
  enableLinkify?: boolean
  enableTypographer?: boolean
}

// Markdown渲染组件Emits
export interface MarkdownViewerEmits {
  contentRendered: [html: string]
  renderError: [error: string]
}

// Sprint 03 新增类型定义 - 文件浏览相关

// 分页信息
export interface FilePagination {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// 文件过滤器
export interface FileFilters {
  search: string | null
  type: string | null
}

// 文件列表响应
export interface FileListResponse {
  subjectId: number
  parentId: number | null
  files: FileNode[]
  pagination: FilePagination
  filters: FileFilters
}

// 面包屑导航项
export interface BreadcrumbItem {
  id: number | null
  name: string
  type: 'subject' | 'folder' | 'file'
  level: number
}

// 面包屑导航响应
export interface BreadcrumbResponse {
  nodeId: number
  subjectId: number
  breadcrumb: BreadcrumbItem[]
  currentNode: {
    id: number
    name: string
    type: string
  }
}

// 搜索高亮信息
export interface SearchHighlight {
  original: string
  highlighted: string
  matchIndex: number
  matchLength: number
}

// 搜索结果项
export interface SearchResultItem extends FileNode {
  relevance_score: number
  highlight: SearchHighlight
}

// 搜索选项
export interface SearchOptions {
  type: string
  includeContent: boolean
  limit: number
}

// 文件搜索响应
export interface FileSearchResponse {
  subjectId: number
  keyword: string
  results: SearchResultItem[]
  total: number
  searchOptions: SearchOptions
}

// 文件列表查询参数
export interface FileListParams {
  page?: number
  limit?: number
  type?: 'file' | 'folder'
  parentId?: number
  search?: string
}

// 文件搜索查询参数
export interface FileSearchParams {
  q: string
  limit?: number
  type?: 'file' | 'folder'
}
