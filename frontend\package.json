{"name": "qimofuxi-frontend", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:components": "playwright test tests/components", "test:pages": "playwright test tests/pages", "test:states": "playwright test tests/states", "test:responsive": "playwright test tests/responsive", "test:report": "playwright show-report", "test:e2e": "node tests/e2e/run-e2e-tests.js", "test:e2e:headed": "node tests/e2e/run-e2e-tests.js --headed", "test:e2e:debug": "node tests/e2e/run-e2e-tests.js --headed --debug", "test:e2e:chrome": "node tests/e2e/run-e2e-tests.js --browser chromium", "test:e2e:firefox": "node tests/e2e/run-e2e-tests.js --browser firefox", "test:e2e:safari": "node tests/e2e/run-e2e-tests.js --browser webkit", "test:e2e:mobile": "node tests/e2e/run-e2e-tests.js --browser 'Mobile Chrome' --browser 'Mobile Safari'", "test:e2e:story": "node tests/e2e/run-e2e-tests.js --grep '完整用户故事'", "test:e2e:report": "npx playwright show-report playwright-report/e2e"}, "dependencies": {"@types/markdown-it": "^14.1.2", "@vueuse/core": "^10.7.2", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "cors": "^2.8.5", "dayjs": "^1.11.10", "express": "^5.1.0", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.16", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "@unocss/eslint-plugin": "^0.58.4", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "playwright": "^1.54.1", "prettier": "^3.2.5", "typescript": "~5.3.3", "unocss": "^0.58.4", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.12", "vue-tsc": "^1.8.27"}}