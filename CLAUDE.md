# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Final Exam Review Platform** (期末复习平台) that allows administrators to upload Markdown study materials organized by subjects, and provides public access for students to read these materials online.

**Architecture**: Monorepo with separate frontend (Vue 3) and backend (Node.js/Express) services using SQLite for data storage.

## Development Commands

### Backend (Node.js/Express)
Navigate to `backend/` directory:
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run Jest unit tests
- `npm run test:playwright` - Run Playwright API tests
- `npm run test:api` - Run API-specific Playwright tests
- `npm run test:report` - Show Playwright test report
- `npm run test:cleanup` - Clean up test data

### Frontend (Vue 3/TypeScript)
Navigate to `frontend/` directory:
- `npm run dev` - Start Vite development server
- `npm run build` - Build for production (runs type-check first)
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking
- `npm test` - Run all Playwright tests
- `npm run test:components` - Test specific component layer
- `npm run test:e2e` - Run end-to-end tests
- `npm run test:report` - Show test reports

### Database Management
From project root:
- Initialize database: `sqlite3 data/database.sqlite ".read data/init_database.sql"`
- View schema: `sqlite3 data/database.sqlite ".schema subjects"`
- Check data: `sqlite3 data/database.sqlite "SELECT * FROM subjects;"`

## Architecture Details

### Backend Structure
- **Entry point**: `backend/app.js` - Express server with security middleware, CORS, rate limiting
- **API Routes**: 
  - `/api/subjects` - Subject management (CRUD operations)
  - `/api/subjects/:id/upload` - File upload to specific subject
  - `/api/files/:fileId` - File retrieval and content access
- **Database**: SQLite with sql.js driver, connection managed in `backend/config/database.js`
- **File Storage**: Physical files stored in `uploads/{subjectId}/` directories
- **Services**: Business logic separated into `backend/services/` (subjectService, fileService)

### Frontend Structure
- **Framework**: Vue 3 with Composition API, TypeScript, Ant Design Vue
- **Routing**: Vue Router with lazy-loaded components
- **State Management**: Pinia stores in `src/stores/`
- **Key Components**:
  - `SubjectManager.vue` - Subject CRUD with card/table views
  - `FileUploader.vue` - File upload with validation
  - `MarkdownViewer.vue` - Markdown rendering with syntax highlighting
- **Build**: Vite with UnoCSS for styling

### Database Schema
**subjects table**:
- `id` (PRIMARY KEY) - Auto-increment subject identifier
- `name` (VARCHAR(50), UNIQUE) - Subject name
- `description` (TEXT) - Optional subject description
- `created_at`, `updated_at` - Timestamps

**Planned file_nodes table** (for file structure):
- Tree structure to maintain folder hierarchy
- Links to physical file storage paths
- Subject association via foreign key

### API Design Patterns
- **Consistent Response Format**:
  ```json
  {
    "success": true,
    "data": {},
    "message": "操作成功",
    "timestamp": "ISO-string",
    "requestId": "string",
    "responseTime": "Xms"
  }
  ```
- **Error Handling**: Centralized error middleware with structured error codes
- **Validation**: Input sanitization and validation middleware on all routes
- **Security**: Helmet, CORS, rate limiting, file type validation

## Development Patterns

### File Organization
- Backend uses CommonJS modules with `require()`
- Frontend uses ES modules with TypeScript
- Services separated from routes/controllers
- Middleware organized by function (validation, error handling, file handling)

### Component Architecture (Frontend)
- Composition API with `<script setup>`
- TypeScript interfaces in `src/types/`
- Reusable components exported from `src/components/index.ts`
- Responsive design with UnoCSS atomic classes

### Testing Strategy
- **Backend**: Playwright for API testing, Jest for unit tests
- **Frontend**: Playwright for E2E and component testing
- Test files organized by feature/layer
- Comprehensive test coverage for user flows and API endpoints

### Error Handling
- Backend: Async error wrapper with structured error responses
- Frontend: Try-catch blocks with user-friendly error messages
- Database errors handled with transaction rollback

## File Upload Flow
1. Frontend selects files via `<input>` with validation
2. Backend receives via multer middleware with file type/size checks
3. Files stored in `uploads/{subjectId}/` with timestamp naming
4. Database records file metadata (planned: file_nodes table)
5. Markdown content processed for image path resolution

## Common Gotchas
- Database uses sql.js (in-memory) - must call `saveDatabase()` to persist
- File uploads limited to 10MB and Markdown files only
- Frontend runs on port 5173 (Vite), backend on port 3001
- CORS configured for development origins, needs production URL update
- SQLite database file location: `data/database.sqlite`

## Key Dependencies
- **Backend**: express, sql.js, multer, helmet, cors, express-rate-limit
- **Frontend**: vue, typescript, ant-design-vue, markdown-it, axios, pinia
- **Testing**: @playwright/test (both frontend/backend)
- **Build**: vite, vue-tsc, unocss

When running tests, always clean up test data using the provided cleanup scripts to avoid database pollution between test runs.