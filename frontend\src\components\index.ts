// 学科管理组件导出
export { default as SubjectCard } from './SubjectCard.vue'
export { default as CreateSubjectModal } from './CreateSubjectModal.vue'
export { default as SubjectManager } from './SubjectManager.vue'

// 文件管理组件导出
export { default as FileUploader } from './FileUploader.vue'
export { default as MarkdownViewer } from './MarkdownViewer.vue'
export { default as FileBrowser } from './FileBrowser.vue'
export { default as BreadcrumbNav } from './BreadcrumbNav.vue'
export { default as FileSearch } from './FileSearch.vue'

// 组件类型导出
export type {
  SubjectCardProps,
  SubjectCardEmits,
  CreateSubjectModalProps,
  CreateSubjectModalEmits,
  SubjectManagerState,
  SubjectTableColumn,
  ViewMode,
  SortOption
} from '@/types/subject'

export type {
  FileUploaderProps,
  FileUploaderEmits,
  MarkdownViewerProps,
  MarkdownViewerEmits,
  UploadStatus
} from '@/types/file'
