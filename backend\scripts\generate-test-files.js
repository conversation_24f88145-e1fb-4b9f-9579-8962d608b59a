const fs = require('fs')
const path = require('path')
const initSqlJs = require('sql.js')

// 数据库路径
const dbPath = path.join(__dirname, '../../data/database.sqlite')

// 简化的测试数据生成
async function generateTestData() {
  console.log('开始生成虚拟滚动测试数据...')

  try {
    const SQL = await initSqlJs()
    const filebuffer = fs.readFileSync(dbPath)
    const db = new SQL.Database(filebuffer)

    // 清理现有测试数据
    db.run('DELETE FROM file_nodes WHERE name LIKE "测试文件_%"')

    // 生成150个测试文件
    const fileExtensions = ['.md', '.txt', '.pdf', '.doc', '.ppt', '.jpg', '.png']
    const folderNames = ['第一章', '第二章', '第三章', '作业', '练习', '资料', '笔记', '课件']

    for (let i = 1; i <= 150; i++) {
      const isFolder = Math.random() < 0.2 // 20% 概率是文件夹
      const type = isFolder ? 'folder' : 'file'

      let name, filePath, fileSize

      if (isFolder) {
        name = `${folderNames[Math.floor(Math.random() * folderNames.length)]}_${i}`
        filePath = `/uploads/subject_2/${name}`
        fileSize = null
      } else {
        const ext = fileExtensions[Math.floor(Math.random() * fileExtensions.length)]
        name = `测试文件_${String(i).padStart(3, '0')}${ext}`
        filePath = `/uploads/subject_2/${name}`
        fileSize = Math.floor(Math.random() * 1024 * 1024) + 1024 // 1KB - 1MB
      }

      db.run(`
        INSERT INTO file_nodes (
          subject_id, parent_id, name, type, file_path, file_size,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `, [2, null, name, type, filePath, fileSize])
    }

    // 保存数据库
    const data = db.export()
    fs.writeFileSync(dbPath, data)

    // 验证结果
    const result = db.exec('SELECT COUNT(*) as count FROM file_nodes WHERE subject_id = 2')
    if (result.length > 0) {
      console.log(`学科2现在共有 ${result[0].values[0][0]} 个文件`)
    }

    console.log('测试数据生成完成！')

  } catch (error) {
    console.error('生成测试数据失败:', error.message)
  }
}

generateTestData()


