// 文件管理服务
// 处理文件上传、存储、检索等业务逻辑

const fs = require('fs');
const path = require('path');
const { getDatabase, saveDatabase } = require('../config/database');
const { validateFilePath, cleanupTempFile } = require('../middleware/fileValidation');

class FileService {
    constructor() {
        this.uploadsDir = path.join(process.cwd(), 'uploads');
        this.tempDir = path.join(this.uploadsDir, 'temp');

        // 确保上传目录存在
        this.ensureDirectoryExists(this.uploadsDir);
        this.ensureDirectoryExists(this.tempDir);
    }

    // 确保目录存在
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`📁 创建目录: ${dirPath}`);
        }
    }

    // 上传文件到指定学科
    async uploadFile(subjectId, file) {
        const startTime = Date.now();
        console.log(`📤 开始上传文件: ${file.originalname} 到学科 ${subjectId}`);

        try {
            // 验证学科是否存在
            await this.validateSubjectExists(subjectId);

            // 创建学科专用目录
            const subjectDir = path.join(this.uploadsDir, subjectId.toString());
            this.ensureDirectoryExists(subjectDir);

            // 生成安全的文件名
            const safeFileName = this.generateSafeFileName(file.originalname);
            const finalPath = path.join(subjectDir, safeFileName);

            // 读取文件内容
            const content = fs.readFileSync(file.path, 'utf8');

            // 移动文件到最终位置
            fs.copyFileSync(file.path, finalPath);

            // 保存文件信息到数据库
            const fileRecord = await this.saveFileRecord({
                subjectId: parseInt(subjectId),
                name: safeFileName,
                originalName: file.originalname,
                content: content,
                filePath: path.relative(process.cwd(), finalPath),
                fileSize: file.size,
                mimeType: file.mimetype
            });

            // 保存数据库到磁盘
            saveDatabase();

            // 清理临时文件
            cleanupTempFile(file.path);

            const responseTime = Date.now() - startTime;
            console.log(`✅ 文件上传成功: ${safeFileName} (${responseTime}ms)`);

            return {
                success: true,
                message: '文件上传成功',
                data: {
                    id: fileRecord.id,
                    name: fileRecord.name,
                    originalName: file.originalname,
                    size: file.size,
                    mimeType: file.mimetype,
                    subjectId: parseInt(subjectId),
                    uploadTime: fileRecord.created_at
                },
                timestamp: new Date().toISOString(),
                responseTime: `${responseTime}ms`
            };

        } catch (error) {
            // 清理临时文件
            cleanupTempFile(file.path);

            console.error(`❌ 文件上传失败: ${error.message}`);
            throw error;
        }
    }

    // 验证学科是否存在
    async validateSubjectExists(subjectId) {
        const database = await getDatabase();

        try {
            const result = database.exec(
                'SELECT id FROM subjects WHERE id = ?',
                [parseInt(subjectId)]
            );

            if (!result.length || !result[0].values.length) {
                const error = new Error(`学科不存在 (ID: ${subjectId})`);
                error.code = 'SUBJECT_NOT_FOUND';
                error.statusCode = 404;
                throw error;
            }

            return true;
        } catch (error) {
            if (error.code === 'SUBJECT_NOT_FOUND') {
                throw error;
            }

            const dbError = new Error('验证学科存在性时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 生成安全的文件名
    generateSafeFileName(originalName) {
        // 获取文件扩展名
        const ext = path.extname(originalName);
        const baseName = path.basename(originalName, ext);

        // 清理文件名，移除不安全字符
        const safeName = baseName
            .replace(/[<>:"/\\|?*\x00-\x1f]/g, '_')
            .replace(/\s+/g, '_')
            .substring(0, 200); // 限制长度

        // 添加时间戳避免冲突
        const timestamp = Date.now();

        return `${safeName}_${timestamp}${ext}`;
    }

    // 保存文件记录到数据库
    async saveFileRecord(fileData) {
        const database = await getDatabase();

        try {
            const insertSQL = `
                INSERT INTO file_nodes (
                    subject_id, parent_id, name, type, content, 
                    file_path, file_size, mime_type, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            `;

            database.run(insertSQL, [
                fileData.subjectId,
                null, // parent_id为null表示根级文件
                fileData.name,
                'file',
                fileData.content,
                fileData.filePath,
                fileData.fileSize,
                fileData.mimeType
            ]);

            // 获取插入的记录ID
            const result = database.exec('SELECT last_insert_rowid() as id');
            const fileId = result[0].values[0][0];

            // 获取完整的文件记录
            const selectResult = database.exec(
                'SELECT * FROM file_nodes WHERE id = ?',
                [fileId]
            );

            if (!selectResult.length || !selectResult[0].values.length) {
                throw new Error('无法获取已插入的文件记录');
            }

            const columns = selectResult[0].columns;
            const values = selectResult[0].values[0];
            const record = {};

            columns.forEach((col, index) => {
                record[col] = values[index];
            });

            console.log(`💾 文件记录已保存到数据库: ID ${fileId}`);
            return record;

        } catch (error) {
            console.error('❌ 保存文件记录失败:', error.message);

            const dbError = new Error('保存文件信息到数据库时发生错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 根据文件ID获取文件内容（专用于内容读取）
    async getFileContent(fileId) {
        const database = await getDatabase();

        try {
            const result = database.exec(
                'SELECT id, name, content, type, file_path, file_size, mime_type, created_at, updated_at FROM file_nodes WHERE id = ? AND type = "file"',
                [parseInt(fileId)]
            );

            if (!result.length || !result[0].values.length) {
                const error = new Error(`文件不存在 (ID: ${fileId})`);
                error.code = 'FILE_NOT_FOUND';
                error.statusCode = 404;
                throw error;
            }

            const columns = result[0].columns;
            const values = result[0].values[0];
            const record = {};

            columns.forEach((col, index) => {
                record[col] = values[index];
            });

            // 验证文件路径安全性
            if (record.file_path) {
                const safePath = validateFilePath(record.file_path);
                record.file_path = safePath;
            }

            console.log(`📄 获取文件内容: ${record.name} (ID: ${fileId})`);

            return {
                success: true,
                message: '获取文件内容成功',
                data: {
                    id: record.id,
                    name: record.name,
                    content: record.content || '', // 确保content字段存在
                    type: record.type,
                    filePath: record.file_path,
                    fileSize: record.file_size,
                    mimeType: record.mime_type,
                    createdAt: record.created_at,
                    updatedAt: record.updated_at
                },
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            if (error.code === 'FILE_NOT_FOUND') {
                throw error;
            }

            console.error('❌ 获取文件内容失败:', error.message);

            const dbError = new Error('获取文件内容时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 根据文件ID获取文件信息
    async getFileById(fileId) {
        const database = await getDatabase();

        try {
            const result = database.exec(
                'SELECT * FROM file_nodes WHERE id = ? AND type = "file"',
                [parseInt(fileId)]
            );

            if (!result.length || !result[0].values.length) {
                const error = new Error(`文件不存在 (ID: ${fileId})`);
                error.code = 'FILE_NOT_FOUND';
                error.statusCode = 404;
                throw error;
            }

            const columns = result[0].columns;
            const values = result[0].values[0];
            const record = {};

            columns.forEach((col, index) => {
                record[col] = values[index];
            });

            return {
                success: true,
                message: '获取文件信息成功',
                data: record,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            if (error.code === 'FILE_NOT_FOUND') {
                throw error;
            }

            const dbError = new Error('获取文件信息时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 根据学科ID获取文件列表 (扩展支持分页和层级浏览)
    async getFilesBySubject(subjectId, options = {}) {
        const database = await getDatabase();
        const startTime = Date.now();

        try {
            // 验证学科是否存在
            await this.validateSubjectExists(subjectId);

            // 解析选项参数
            const {
                parentId = null,
                page = 1,
                limit = 50,
                search = null,
                type = null // 'file' | 'folder' | null (all)
            } = options;

            // 构建查询条件
            let whereConditions = ['subject_id = ?'];
            let queryParams = [parseInt(subjectId)];

            // 父目录条件
            if (parentId === null) {
                whereConditions.push('parent_id IS NULL');
            } else {
                whereConditions.push('parent_id = ?');
                queryParams.push(parseInt(parentId));
            }

            // 类型过滤
            if (type) {
                whereConditions.push('type = ?');
                queryParams.push(type);
            }

            // 搜索条件
            if (search && search.trim().length > 0) {
                whereConditions.push('LOWER(name) LIKE LOWER(?)');
                queryParams.push(`%${search.trim()}%`);
            }

            // 计算分页
            const offset = (parseInt(page) - 1) * parseInt(limit);

            // 构建查询SQL
            const whereClause = whereConditions.join(' AND ');
            const baseQuery = `
                SELECT id, subject_id, parent_id, name, type, file_path, file_size, mime_type, created_at, updated_at
                FROM file_nodes
                WHERE ${whereClause}
            `;

            // 获取总数
            const countQuery = `SELECT COUNT(*) as total FROM file_nodes WHERE ${whereClause}`;
            const countResult = database.exec(countQuery, queryParams);
            const total = countResult[0].values[0][0];

            // 获取分页数据 (文件夹优先，然后按名称排序)
            const dataQuery = `${baseQuery} ORDER BY type DESC, LOWER(name) ASC LIMIT ? OFFSET ?`;
            const dataParams = [...queryParams, parseInt(limit), offset];
            const result = database.exec(dataQuery, dataParams);

            let files = [];
            if (result.length > 0 && result[0].values.length > 0) {
                const columns = result[0].columns;
                files = result[0].values.map(values => {
                    const record = {};
                    columns.forEach((col, index) => {
                        record[col] = values[index];
                    });
                    return record;
                });
            }

            // 计算分页信息
            const totalPages = Math.ceil(total / parseInt(limit));
            const hasNextPage = parseInt(page) < totalPages;
            const hasPrevPage = parseInt(page) > 1;

            const responseTime = Date.now() - startTime;
            console.log(`📋 获取学科${subjectId}的文件列表: ${files.length}/${total}个项目 (${responseTime}ms)`);

            return {
                success: true,
                message: '获取文件列表成功',
                data: {
                    subjectId: parseInt(subjectId),
                    parentId: parentId ? parseInt(parentId) : null,
                    files: files,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: total,
                        totalPages: totalPages,
                        hasNextPage: hasNextPage,
                        hasPrevPage: hasPrevPage
                    },
                    filters: {
                        search: search || null,
                        type: type || null
                    }
                },
                timestamp: new Date().toISOString(),
                responseTime: `${responseTime}ms`
            };

        } catch (error) {
            if (error.code === 'SUBJECT_NOT_FOUND') {
                throw error;
            }

            console.error('❌ 获取文件列表失败:', error.message);

            const dbError = new Error('获取文件列表时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 获取文件或文件夹的面包屑导航路径
    async getBreadcrumb(nodeId) {
        const database = await getDatabase();
        const startTime = Date.now();

        try {
            // 验证节点是否存在
            const nodeResult = database.exec(
                'SELECT id, subject_id, parent_id, name, type FROM file_nodes WHERE id = ?',
                [parseInt(nodeId)]
            );

            if (!nodeResult.length || !nodeResult[0].values.length) {
                const error = new Error(`文件或文件夹不存在 (ID: ${nodeId})`);
                error.code = 'NODE_NOT_FOUND';
                error.statusCode = 404;
                throw error;
            }

            const nodeData = nodeResult[0].values[0];
            const [id, subjectId, parentId, name, type] = nodeData;

            // 使用递归CTE查询面包屑路径
            const breadcrumbQuery = `
                WITH RECURSIVE breadcrumb_path AS (
                    -- 起始节点
                    SELECT id, parent_id, name, type, 0 as level
                    FROM file_nodes
                    WHERE id = ?

                    UNION ALL

                    -- 递归查询父节点
                    SELECT fn.id, fn.parent_id, fn.name, fn.type, bp.level + 1
                    FROM file_nodes fn
                    INNER JOIN breadcrumb_path bp ON fn.id = bp.parent_id
                )
                SELECT id, name, type, level
                FROM breadcrumb_path
                ORDER BY level DESC
            `;

            const breadcrumbResult = database.exec(breadcrumbQuery, [parseInt(nodeId)]);

            let breadcrumb = [];
            if (breadcrumbResult.length > 0 && breadcrumbResult[0].values.length > 0) {
                const columns = breadcrumbResult[0].columns;
                breadcrumb = breadcrumbResult[0].values.map(values => {
                    const record = {};
                    columns.forEach((col, index) => {
                        record[col] = values[index];
                    });
                    return {
                        id: record.id,
                        name: record.name,
                        type: record.type,
                        level: record.level
                    };
                });
            }

            // 添加根目录项（学科根目录）
            const subjectResult = database.exec(
                'SELECT name FROM subjects WHERE id = ?',
                [subjectId]
            );

            if (subjectResult.length > 0 && subjectResult[0].values.length > 0) {
                const subjectName = subjectResult[0].values[0][0];
                breadcrumb.unshift({
                    id: null,
                    name: subjectName,
                    type: 'subject',
                    level: breadcrumb.length
                });
            }

            const responseTime = Date.now() - startTime;
            console.log(`🧭 获取面包屑导航: ${breadcrumb.length}级路径 (${responseTime}ms)`);

            return {
                success: true,
                message: '获取面包屑导航成功',
                data: {
                    nodeId: parseInt(nodeId),
                    subjectId: subjectId,
                    breadcrumb: breadcrumb,
                    currentNode: {
                        id: id,
                        name: name,
                        type: type
                    }
                },
                timestamp: new Date().toISOString(),
                responseTime: `${responseTime}ms`
            };

        } catch (error) {
            if (error.code === 'NODE_NOT_FOUND') {
                throw error;
            }

            console.error('❌ 获取面包屑导航失败:', error.message);

            const dbError = new Error('获取面包屑导航时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 在指定学科内搜索文件和文件夹
    async searchFiles(subjectId, keyword, options = {}) {
        const database = await getDatabase();
        const startTime = Date.now();

        try {
            // 验证学科是否存在
            await this.validateSubjectExists(subjectId);

            // 验证搜索关键词
            if (!keyword || keyword.trim().length < 2) {
                const error = new Error('搜索关键词至少需要2个字符');
                error.code = 'INVALID_SEARCH_KEYWORD';
                error.statusCode = 400;
                throw error;
            }

            // 解析选项参数
            const {
                type = null, // 'file' | 'folder' | null (all)
                limit = 20,
                includeContent = false // 是否在文件内容中搜索
            } = options;

            const searchTerm = keyword.trim();
            const searchPattern = `%${searchTerm}%`;

            // 构建查询条件
            let whereConditions = ['subject_id = ?'];
            let queryParams = [parseInt(subjectId)];

            // 类型过滤
            if (type) {
                whereConditions.push('type = ?');
                queryParams.push(type);
            }

            // 搜索条件 (文件名 + 可选的内容搜索)
            let searchConditions = ['LOWER(name) LIKE LOWER(?)'];
            queryParams.push(searchPattern);

            if (includeContent) {
                searchConditions.push('LOWER(content) LIKE LOWER(?)');
                queryParams.push(searchPattern);
            }

            whereConditions.push(`(${searchConditions.join(' OR ')})`);

            // 构建查询SQL，使用智能排序
            const searchQuery = `
                SELECT
                    id, subject_id, parent_id, name, type, file_path, file_size, mime_type, created_at, updated_at,
                    -- 计算相关性得分
                    CASE
                        WHEN LOWER(name) = LOWER(?) THEN 100  -- 完全匹配
                        WHEN LOWER(name) LIKE LOWER(?) THEN 90  -- 开头匹配
                        WHEN LOWER(name) LIKE LOWER(?) THEN 80  -- 结尾匹配
                        WHEN LOWER(name) LIKE LOWER(?) THEN 70  -- 包含匹配
                        ${includeContent ? 'WHEN LOWER(content) LIKE LOWER(?) THEN 60' : ''}  -- 内容匹配
                        ELSE 50
                    END as relevance_score
                FROM file_nodes
                WHERE ${whereConditions.join(' AND ')}
                ORDER BY relevance_score DESC, type DESC, LOWER(name) ASC
                LIMIT ?
            `;

            // 构建查询参数 (相关性计算 + 原查询参数 + limit)
            const searchParams = [
                searchTerm,                    // 完全匹配
                `${searchTerm}%`,             // 开头匹配
                `%${searchTerm}`,             // 结尾匹配
                searchPattern,                // 包含匹配
                ...(includeContent ? [searchPattern] : []), // 内容匹配
                ...queryParams,               // 原查询参数
                parseInt(limit)               // 限制数量
            ];

            const result = database.exec(searchQuery, searchParams);

            let searchResults = [];
            if (result.length > 0 && result[0].values.length > 0) {
                const columns = result[0].columns;
                searchResults = result[0].values.map(values => {
                    const record = {};
                    columns.forEach((col, index) => {
                        record[col] = values[index];
                    });

                    // 生成搜索高亮信息
                    const highlightInfo = this.generateHighlightInfo(record.name, searchTerm);

                    return {
                        id: record.id,
                        subject_id: record.subject_id,
                        parent_id: record.parent_id,
                        name: record.name,
                        type: record.type,
                        file_path: record.file_path,
                        file_size: record.file_size,
                        mime_type: record.mime_type,
                        created_at: record.created_at,
                        updated_at: record.updated_at,
                        relevance_score: record.relevance_score,
                        highlight: highlightInfo
                    };
                });
            }

            const responseTime = Date.now() - startTime;
            console.log(`🔍 搜索文件: "${searchTerm}" 找到${searchResults.length}个结果 (${responseTime}ms)`);

            return {
                success: true,
                message: '搜索完成',
                data: {
                    subjectId: parseInt(subjectId),
                    keyword: searchTerm,
                    results: searchResults,
                    total: searchResults.length,
                    searchOptions: {
                        type: type || 'all',
                        includeContent: includeContent,
                        limit: parseInt(limit)
                    }
                },
                timestamp: new Date().toISOString(),
                responseTime: `${responseTime}ms`
            };

        } catch (error) {
            if (error.code === 'SUBJECT_NOT_FOUND' || error.code === 'INVALID_SEARCH_KEYWORD') {
                throw error;
            }

            console.error('❌ 搜索文件失败:', error.message);

            const dbError = new Error('搜索文件时发生数据库错误');
            dbError.code = 'DATABASE_ERROR';
            dbError.statusCode = 500;
            dbError.originalError = error;
            throw dbError;
        }
    }

    // 生成搜索结果高亮信息
    generateHighlightInfo(text, keyword) {
        if (!text || !keyword) return { original: text, highlighted: text };

        const lowerText = text.toLowerCase();
        const lowerKeyword = keyword.toLowerCase();
        const index = lowerText.indexOf(lowerKeyword);

        if (index === -1) {
            return { original: text, highlighted: text };
        }

        // 生成高亮版本
        const before = text.substring(0, index);
        const match = text.substring(index, index + keyword.length);
        const after = text.substring(index + keyword.length);

        return {
            original: text,
            highlighted: `${before}<mark>${match}</mark>${after}`,
            matchIndex: index,
            matchLength: keyword.length
        };
    }
}

module.exports = new FileService();
