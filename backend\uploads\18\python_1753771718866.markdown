### 一、Python 简介

- **Python 是一种解释型语言：** 这意味着开发过程中没有了编译这个环节。类似于PHP和Perl语言。
- **Python 是交互式语言：** 这意味着，您可以在一个 Python 提示符 **>>>** 后直接执行代码。
- **Python 是面向对象语言:** 这意味着Python支持面向对象的风格或代码封装在对象的编程技术。
- **Python 是初学者的语言：**Python 对初级程序员而言，是一种伟大的语言，它支持广泛的应用程序开发，从简单的文字处理到 WWW 浏览器再到游戏。

### 二、安装 Python

#### 1.安装miniaconda

下载网站：[anaconda | 镜像站使用帮助 | 清华大学开源软件镜像站 | Tsinghua Open Source Mirror](https://mirrors.tuna.tsinghua.edu.cn/help/anaconda/)

常用指令：[Miniconda的常用命令 - 术科术 - 博客园](https://www.cnblogs.com/shukeshu/p/17516978.html)

#### 2.创建虚拟环境

​    使用conda创建虚拟环境的命令格式为:

```bash
conda create -n env_name python=3.8
```

​    这表示创建python版本为3.8、名字为env_name的虚拟环境。

​    创建后，env_name文件可以在Anaconda安装目录envs文件下找到。在不指定python版本时，自动创建基于最新python版本的虚拟环境.  

​    使用如下命令即可激活创建的虚拟环境。

```bash
conda activate env_name
```

安装opencv

```
pip install opencv-python


pip install opencv-python  -i https://pypi.tuna.tsinghua.edu.cn/simple
检测安装效果
import sys
import cv2

print(sys.version)
print(cv2.__version__)
```



#### 3.使用vscode 的python扩展

<img src="python.assets/image-20250113153947856.png" alt="image-20250113153947856" style="zoom:33%;" />

### 三、Python 基础语法

[Python3 教程 | 菜鸟教程](https://www.runoob.com/python3/python3-tutorial.html)

