<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center h-16">
          <router-link :to="`/subjects/${subjectId}`" class="text-gray-500 hover:text-gray-700 mr-4">
            <i class="i-carbon-arrow-left text-xl"></i>
          </router-link>
          <div class="flex items-center">
            <i class="i-carbon-document text-xl text-blue-600 mr-2"></i>
            <h1 class="text-xl font-bold text-gray-900">{{ displayFileName }}</h1>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex-center py-12">
        <a-spin size="large" />
        <p class="text-gray-600 ml-4">正在加载文件内容...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="text-center py-12">
        <i class="i-carbon-warning text-4xl text-red-500 mb-4"></i>
        <p class="text-red-600 mb-4">{{ errorMessage }}</p>
        <button @click="handleRefresh" class="btn-primary">
          重新加载
        </button>
      </div>

      <!-- 文件内容 -->
      <div v-else-if="currentFile" class="space-y-6">
        <!-- 文件信息卡片 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex-center mr-4">
                <i class="i-carbon-document text-2xl text-blue-600"></i>
              </div>
              <div>
                <h2 class="text-xl font-bold text-gray-900">{{ fileName }}</h2>
                <p class="text-gray-600">文件 ID: {{ fileId }}</p>
              </div>
            </div>
            <div class="text-right text-sm text-gray-500">
              <p>大小: {{ formatFileSize(fileSize) }}</p>
              <p>类型: {{ mimeType }}</p>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-1">创建时间</h4>
              <p class="text-gray-900">{{ formatDateTime(currentFile.data.createdAt) }}</p>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-1">更新时间</h4>
              <p class="text-gray-900">{{ formatDateTime(currentFile.data.updatedAt) }}</p>
            </div>
          </div>
        </div>

        <!-- Markdown内容 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              <i class="i-carbon-view text-lg mr-2"></i>
              文件内容
            </h3>
          </div>
          <div class="p-6">
            <MarkdownViewer 
              :content="fileContent"
              :loading="false"
            />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFileStore } from '@/stores'
import { MarkdownViewer } from '@/components'
import dayjs from 'dayjs'

const route = useRoute()
const fileStore = useFileStore()

// 路由参数
const fileId = computed(() => Number(route.params.fileId))
const subjectId = computed(() => Number(route.params.subjectId))

// 计算属性
const currentFile = computed(() => fileStore.currentFile)
const isLoading = computed(() => fileStore.isLoading)
const hasError = computed(() => fileStore.hasError)
const errorMessage = computed(() => fileStore.errorMessage)
const fileContent = computed(() => fileStore.fileContent)
const fileName = computed(() => fileStore.fileName)
const fileSize = computed(() => fileStore.fileSize)
const mimeType = computed(() => fileStore.mimeType)

// 显示文件名（去掉时间戳后缀）
const displayFileName = computed(() => {
  const name = fileName.value
  if (!name) return '文件详情'
  
  // 去掉时间戳后缀，例如 "example_1640995200000.md" -> "example.md"
  const match = name.match(/^(.+)_\d+(\.\w+)$/)
  return match ? `${match[1]}${match[2]}` : name
})

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期时间
const formatDateTime = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 刷新数据
const handleRefresh = () => {
  if (fileId.value) {
    fileStore.getFileContent(fileId.value)
  }
}

// 页面加载时获取数据
onMounted(async () => {
  if (fileId.value) {
    try {
      await fileStore.getFileContent(fileId.value)
    } catch (error) {
      console.error('获取文件内容失败:', error)
    }
  } else {
    message.error('无效的文件ID')
  }
})

// 页面卸载时清除当前文件
onUnmounted(() => {
  fileStore.clearCurrentFile()
})
</script>
