# FileBrowser 功能实现指南

## 文档信息
- **版本**: v1.0.0
- **创建日期**: 2025-07-30
- **维护者**: <PERSON> (工程师) + Emma (产品经理)
- **状态**: Sprint 03完成 - 文件浏览功能全面实现

## 概述

本文档详细介绍期末复习平台文件浏览功能的完整实现，包括FileBrowser、FileSearch、BreadcrumbNav三大核心组件的设计思路、技术实现和使用指南。

## 核心组件架构

### 1. FileBrowser 主容器组件

**设计理念**：
- 作为文件浏览的主容器，集成所有子组件
- 支持多种视图模式（列表/网格）和虚拟滚动优化
- 提供完整的文件操作和导航功能

**核心特性**：
```typescript
interface FileBrowserProps {
  subjectId: number                    // 学科ID
  initialParentId?: number | null      // 初始父目录ID
  height?: string | number             // 容器高度
  showBreadcrumb?: boolean             // 是否显示面包屑
  enableSearch?: boolean               // 是否启用搜索
  enableVirtualScroll?: boolean        // 是否启用虚拟滚动
  virtualScrollItemHeight?: number     // 虚拟滚动项高度
  virtualScrollVisibleCount?: number   // 可见项数量
}

interface FileBrowserEmits {
  fileClick: [file: FileNode]                           // 文件点击事件
  fileDoubleClick: [file: FileNode]                     // 文件双击事件
  folderEnter: [folder: FileNode]                       // 进入文件夹事件
  breadcrumbClick: [item: BreadcrumbItem, index: number] // 面包屑点击事件
}
```

**实现要点**：
1. **状态管理**：使用Vue 3 Composition API管理组件状态
2. **API集成**：通过fileApi调用后端接口获取数据
3. **虚拟滚动**：集成useVirtualScroll组合式函数优化性能
4. **响应式设计**：支持桌面端和移动端自适应布局

### 2. FileSearch 搜索组件

**设计理念**：
- 提供实时搜索功能，支持防抖优化
- 管理搜索历史，提供智能搜索建议
- 结果高亮显示，提升用户体验

**核心特性**：
```typescript
interface FileSearchProps {
  subjectId: number           // 学科ID
  placeholder?: string        // 搜索框占位符
  disabled?: boolean          // 是否禁用
  showHistory?: boolean       // 是否显示历史
  showResults?: boolean       // 是否显示结果
  autoSearch?: boolean        // 是否自动搜索
  debounceDelay?: number      // 防抖延迟
  maxHistoryItems?: number    // 最大历史项数
  resultLimit?: number        // 结果数量限制
}

interface FileSearchEmits {
  search: [keyword: string, results: SearchResultItem[]]  // 搜索事件
  resultClick: [result: SearchResultItem]                 // 结果点击事件
  historyChange: [history: string[]]                      // 历史变化事件
  focus: []                                               // 获得焦点事件
  blur: []                                                // 失去焦点事件
}
```

**实现要点**：
1. **防抖搜索**：使用lodash-es的debounce函数优化搜索性能
2. **历史管理**：使用localStorage存储用户搜索历史
3. **结果高亮**：服务端返回高亮HTML，前端直接渲染
4. **智能排序**：按相关度评分排序搜索结果

### 3. BreadcrumbNav 导航组件

**设计理念**：
- 提供直观的路径导航功能
- 支持智能省略和响应式布局
- 提供丰富的主题和配置选项

**核心特性**：
```typescript
interface BreadcrumbNavProps {
  items: BreadcrumbItem[]     // 面包屑项目
  maxItems?: number           // 最大显示项数
  separator?: string          // 分隔符
  loading?: boolean           // 加载状态
  showIcons?: boolean         // 是否显示图标
  clickable?: boolean         // 是否可点击
  theme?: 'light' | 'dark'    // 主题
  size?: 'small' | 'medium' | 'large'  // 尺寸
  responsive?: boolean        // 是否响应式
}

interface BreadcrumbNavEmits {
  itemClick: [item: BreadcrumbItem, index: number]  // 项目点击事件
  backClick: [previousItem: BreadcrumbItem]         // 返回点击事件
  pathChange: [newPath: BreadcrumbItem[]]           // 路径变化事件
}
```

**实现要点**：
1. **智能省略**：长路径自动省略中间项，保留首末项
2. **响应式设计**：768px断点切换桌面/移动端模式
3. **主题支持**：支持浅色/深色主题切换
4. **交互优化**：悬停效果、键盘导航、触摸友好

## 虚拟滚动实现

### useVirtualScroll 组合式函数

**设计目标**：
- 支持大量数据的高性能渲染
- 提供动态高度和缓冲区管理
- 保持60fps的流畅滚动体验

**核心实现**：
```typescript
export function useVirtualScroll(options: VirtualScrollOptions) {
  const {
    items,
    itemHeight,
    visibleCount,
    buffer = 5
  } = options

  const containerRef = ref<HTMLElement>()
  const listRef = ref<HTMLElement>()
  const scrollTop = ref(0)
  
  // 计算可见项目
  const visibleItems = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight) - buffer
    const end = start + visibleCount + buffer * 2
    
    return {
      items: items.value.slice(Math.max(0, start), end),
      startIndex: Math.max(0, start),
      offsetY: Math.max(0, start) * itemHeight
    }
  })

  // 滚动处理
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
    
    // 预加载逻辑
    const { scrollHeight, clientHeight } = target
    if (scrollHeight - scrollTop.value - clientHeight < 100) {
      // 触发加载更多
      emit('loadMore')
    }
  }

  return {
    containerRef,
    listRef,
    visibleItems,
    scrollTop,
    handleScroll
  }
}
```

**性能优化特性**：
1. **缓冲区管理**：上下各保留5个项目的缓冲区，减少滚动时的闪烁
2. **动态高度支持**：支持不同高度的列表项
3. **内存优化**：只渲染可见区域的DOM节点，大幅减少内存使用
4. **滚动优化**：使用requestAnimationFrame优化滚动性能

## API 集成实现

### 文件列表API

**端点**：`GET /api/subjects/:id/files`

**参数说明**：
```typescript
interface FileListParams {
  page?: number        // 页码，默认1
  limit?: number       // 每页数量，默认50
  type?: 'file' | 'folder'  // 文件类型过滤
  parentId?: number    // 父目录ID，用于层级浏览
  search?: string      // 搜索关键词
}
```

**响应格式**：
```typescript
interface FileListResponse {
  subjectId: number
  parentId: number | null
  files: FileNode[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  filters: {
    search: string | null
    type: string | null
  }
}
```

### 文件搜索API

**端点**：`GET /api/subjects/:id/search`

**参数说明**：
```typescript
interface FileSearchParams {
  q: string           // 搜索关键词，至少2个字符
  limit?: number      // 结果数量限制，默认20
  type?: 'file' | 'folder'  // 文件类型过滤
}
```

**响应格式**：
```typescript
interface FileSearchResponse {
  subjectId: number
  keyword: string
  results: SearchResultItem[]
  total: number
  searchOptions: {
    type: string
    includeContent: boolean
    limit: number
  }
}

interface SearchResultItem extends FileNode {
  relevance_score: number    // 相关度评分 0-100
  highlight: {
    original: string         // 原始文本
    highlighted: string      // 高亮HTML
    matchIndex: number       // 匹配位置
    matchLength: number      // 匹配长度
  }
}
```

### 面包屑导航API

**端点**：`GET /api/files/:id/breadcrumb`

**响应格式**：
```typescript
interface BreadcrumbResponse {
  nodeId: number
  subjectId: number
  breadcrumb: BreadcrumbItem[]
  currentNode: {
    id: number
    name: string
    type: 'file' | 'folder'
  }
}

interface BreadcrumbItem {
  id: number | null    // null表示学科根目录
  name: string         // 显示名称
  type: 'subject' | 'folder' | 'file'  // 节点类型
  level: number        // 层级深度
}
```

## 使用示例

### 基础使用

```vue
<template>
  <div class="file-management-page">
    <FileBrowser
      :subject-id="subjectId"
      :height="600"
      :show-breadcrumb="true"
      :enable-search="true"
      :enable-virtual-scroll="true"
      @file-click="handleFileClick"
      @folder-enter="handleFolderEnter"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { FileBrowser } from '@/components'
import type { FileNode } from '@/types'

const subjectId = ref(1)

const handleFileClick = (file: FileNode) => {
  console.log('文件点击:', file)
  // 处理文件点击逻辑
}

const handleFolderEnter = (folder: FileNode) => {
  console.log('进入文件夹:', folder)
  // 处理文件夹导航逻辑
}
</script>
```

### 高级配置

```vue
<template>
  <FileBrowser
    :subject-id="subjectId"
    :initial-parent-id="parentId"
    :height="'calc(100vh - 200px)'"
    :show-breadcrumb="true"
    :enable-search="true"
    :enable-virtual-scroll="true"
    :virtual-scroll-item-height="120"
    :virtual-scroll-visible-count="20"
    @file-click="handleFileClick"
    @file-double-click="handleFileDoubleClick"
    @folder-enter="handleFolderEnter"
    @breadcrumb-click="handleBreadcrumbClick"
  />
</template>
```

## 性能优化建议

### 1. 虚拟滚动优化
- 大量文件时启用虚拟滚动（>100个文件）
- 根据内容调整项目高度和可见数量
- 使用缓冲区减少滚动时的重新渲染

### 2. 搜索性能优化
- 设置合适的防抖延迟（推荐300ms）
- 限制搜索结果数量（推荐20-50个）
- 使用搜索历史减少重复搜索

### 3. 内存管理
- 及时清理不需要的搜索结果
- 使用计算属性缓存复杂计算
- 避免在模板中使用复杂的方法调用

### 4. 网络优化
- 使用分页加载减少单次请求数据量
- 实现搜索结果缓存
- 使用防抖避免频繁API调用

## 故障排除

### 常见问题

1. **虚拟滚动不流畅**
   - 检查项目高度设置是否正确
   - 减少可见项数量或增加缓冲区
   - 确保容器高度设置正确

2. **搜索结果不准确**
   - 检查搜索关键词长度（至少2个字符）
   - 确认后端搜索算法配置
   - 检查数据库索引是否正确创建

3. **面包屑导航错误**
   - 确认文件层级关系正确
   - 检查API返回的面包屑数据格式
   - 验证父子关系的数据一致性

### 调试技巧

1. **开启开发者工具**：使用Vue DevTools查看组件状态
2. **网络面板**：检查API请求和响应数据
3. **性能面板**：分析虚拟滚动的渲染性能
4. **控制台日志**：查看组件内部的调试信息

## 扩展开发

### 自定义文件类型图标

```typescript
// 在FileBrowser组件中扩展文件图标映射
const getFileIcon = (file: FileNode) => {
  const iconMap = {
    'text/markdown': 'i-carbon-document-text',
    'application/pdf': 'i-carbon-document-pdf',
    'image/png': 'i-carbon-image',
    'image/jpeg': 'i-carbon-image',
    'folder': 'i-carbon-folder'
  }
  
  return iconMap[file.mime_type || file.type] || 'i-carbon-document'
}
```

### 添加文件操作功能

```typescript
// 扩展FileBrowser组件支持文件操作
interface FileBrowserEmits {
  // 现有事件...
  fileDelete: [file: FileNode]
  fileRename: [file: FileNode, newName: string]
  fileMove: [file: FileNode, targetFolder: FileNode]
}
```

### 自定义搜索过滤器

```typescript
// 在FileSearch组件中添加高级过滤
interface AdvancedSearchFilters {
  fileType: string[]      // 文件类型过滤
  dateRange: [Date, Date] // 日期范围过滤
  sizeRange: [number, number] // 文件大小过滤
  tags: string[]          // 标签过滤
}
```

---

**注意**：本文档将随着功能迭代持续更新，请关注版本变更和最佳实践更新。
