<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center h-16">
          <router-link to="/subjects" class="text-gray-500 hover:text-gray-700 mr-4">
            <i class="i-carbon-arrow-left text-xl"></i>
          </router-link>
          <h1 class="text-xl font-bold text-gray-900">学科详情</h1>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex-center py-12">
        <a-spin size="large" />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="text-center py-12">
        <i class="i-carbon-warning text-4xl text-red-500 mb-4"></i>
        <p class="text-red-600 mb-4">{{ errorMessage }}</p>
        <button @click="handleRefresh" class="btn-primary">
          重新加载
        </button>
      </div>

      <!-- 学科详情 -->
      <div v-else-if="currentSubject" class="space-y-6">
        <!-- 基本信息卡片 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-start justify-between mb-6">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex-center mr-4">
                <i class="i-carbon-book text-2xl text-blue-600"></i>
              </div>
              <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ currentSubject.name }}</h2>
                <p class="text-gray-600">学科 ID: {{ currentSubject.id }}</p>
              </div>
            </div>
          </div>

          <!-- 描述 -->
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">学科描述</h3>
            <p v-if="currentSubject.description" class="text-gray-700 leading-relaxed">
              {{ currentSubject.description }}
            </p>
            <p v-else class="text-gray-500 italic">
              暂无描述
            </p>
          </div>

          <!-- 时间信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-1">创建时间</h4>
              <p class="text-gray-900">{{ formatDateTime(currentSubject.created_at) }}</p>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-1">更新时间</h4>
              <p class="text-gray-900">{{ formatDateTime(currentSubject.updated_at) }}</p>
            </div>
          </div>
        </div>

        <!-- 文件管理区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            <i class="i-carbon-folder mr-2"></i>
            文件管理
          </h3>
          <p class="text-gray-600 mb-6">
            浏览、搜索和管理该学科下的复习资料文件，支持层级导航和实时搜索。
          </p>

          <!-- 面包屑导航 -->
          <div v-if="fileStore.breadcrumbItems.length > 0" class="mb-4">
            <BreadcrumbNav
              :items="fileStore.breadcrumbItems"
              :loading="fileStore.isFileListLoading"
              @item-click="handleBreadcrumbClick"
              @back-click="handleBreadcrumbBack"
            />
          </div>

          <!-- 文件浏览器 -->
          <FileBrowser
            :subject-id="currentSubject.id"
            :initial-parent-id="fileStore.currentParentId"
            :show-breadcrumb="false"
            :enable-virtual-scroll="true"
            :virtual-scroll-item-height="120"
            :virtual-scroll-visible-count="20"
            height="600px"
            @file-select="handleFileSelect"
            @folder-navigate="handleFolderNavigate"
            @file-upload="handleFileUploadFromBrowser"
          />
        </div>

        <!-- 快速搜索区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            <i class="i-carbon-search mr-2"></i>
            快速搜索
          </h3>
          <p class="text-gray-600 mb-6">
            快速搜索该学科下的复习资料文件，支持实时搜索和历史记录。
          </p>

          <FileSearch
            :subject-id="currentSubject.id"
            @file-select="handleFileSelect"
          />
        </div>

        <!-- 文件上传区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 file-upload-section">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            <i class="i-carbon-document-add mr-2"></i>
            上传复习资料
          </h3>
          <p class="text-gray-600 mb-6">
            上传 Markdown 格式的复习资料到该学科，支持拖拽上传。
          </p>

          <FileUploader
            :subject-id="currentSubject.id"
            @upload-success="handleUploadSuccess"
            @upload-error="handleUploadError"
          />
        </div>

        <!-- 其他功能区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">其他功能</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="card text-center opacity-60">
              <div class="w-10 h-10 bg-gray-100 rounded-lg flex-center mx-auto mb-3">
                <i class="i-carbon-chart-line text-xl text-gray-400"></i>
              </div>
              <h4 class="font-medium text-gray-500 mb-2">学习统计</h4>
              <p class="text-sm text-gray-400 mb-3">查看学习进度和统计</p>
              <button class="btn" disabled>即将推出</button>
            </div>

            <div class="card text-center opacity-60">
              <div class="w-10 h-10 bg-gray-100 rounded-lg flex-center mx-auto mb-3">
                <i class="i-carbon-settings text-xl text-gray-400"></i>
              </div>
              <h4 class="font-medium text-gray-500 mb-2">学科设置</h4>
              <p class="text-sm text-gray-400 mb-3">编辑学科信息和设置</p>
              <button class="btn" disabled>即将推出</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useSubjectStore, useFileStore } from '@/stores'
import { FileUploader, FileSearch, FileBrowser, BreadcrumbNav } from '@/components'
import type { FileNode } from '@/types'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const subjectStore = useSubjectStore()
const fileStore = useFileStore()

// 计算属性
const currentSubject = computed(() => subjectStore.currentSubject)
const isLoading = computed(() => subjectStore.isLoading)
const hasError = computed(() => subjectStore.hasError)
const errorMessage = computed(() => subjectStore.errorMessage)

// 格式化日期时间
const formatDateTime = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 刷新数据
const handleRefresh = () => {
  const id = Number(route.params.id)
  if (id) {
    subjectStore.getSubjectById(id)
  }
}

// 文件上传成功处理
const handleUploadSuccess = (file: FileNode) => {
  console.log('文件上传成功:', file)
  message.success(`文件 "${file.name}" 上传成功！`)

  // 导航到文件详情页面
  router.push({
    name: 'FileDetail',
    params: {
      subjectId: currentSubject.value?.id,
      fileId: file.id
    }
  })
}

// 文件上传失败处理
const handleUploadError = (error: string) => {
  console.error('文件上传失败:', error)
  message.error(`文件上传失败: ${error}`)
}

// 文件选择处理
const handleFileSelect = (file: FileNode) => {
  console.log('选择文件:', file)
  message.success(`选择了文件: ${file.name}`)

  // 导航到文件详情页面
  router.push({
    name: 'FileDetail',
    params: {
      subjectId: currentSubject.value?.id,
      fileId: file.id
    }
  })
}

// 面包屑导航点击处理
const handleBreadcrumbClick = (item: any) => {
  console.log('面包屑导航点击:', item)
  if (item.id && currentSubject.value) {
    // 导航到指定目录
    fileStore.navigateToDirectory(currentSubject.value.id, item.id)
  }
}

// 面包屑返回处理
const handleBreadcrumbBack = () => {
  console.log('面包屑返回')
  if (currentSubject.value && fileStore.currentParentId) {
    // 返回上级目录
    fileStore.navigateToDirectory(currentSubject.value.id, null)
  }
}

// 文件夹导航处理
const handleFolderNavigate = (folder: FileNode) => {
  console.log('文件夹导航:', folder)
  if (currentSubject.value) {
    // 导航到子目录
    fileStore.navigateToDirectory(currentSubject.value.id, folder.id)
  }
}

// 从文件浏览器上传文件处理
const handleFileUploadFromBrowser = () => {
  console.log('从文件浏览器触发上传')
  // 滚动到文件上传区域
  const uploadSection = document.querySelector('.file-upload-section')
  if (uploadSection) {
    uploadSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// 页面加载时获取数据
onMounted(async () => {
  const id = Number(route.params.id)
  if (id) {
    // 获取学科信息
    await subjectStore.getSubjectById(id)

    // 初始化文件浏览器状态
    try {
      await fileStore.getFileList(id, { page: 1, limit: 20 })
    } catch (error) {
      console.error('初始化文件列表失败:', error)
    }
  }
})

// 页面卸载时清除状态
onUnmounted(() => {
  subjectStore.clearCurrentSubject()
  fileStore.clearFileListState()
  fileStore.clearSearchState()
})
</script>
