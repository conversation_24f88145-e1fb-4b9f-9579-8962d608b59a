# 任务规划文档 - 期末复习平台

## 文档信息

| 项目 | 内容 |
|------|------|
| **产品名称** | 期末复习平台 |
| **规划版本** | v1.0 |
| **创建日期** | 2025-01-28 |
| **负责人** | Emma (产品经理) |
| **基于文档** | PRD_期末复习平台_v1.0.md |
| **方法论** | 垂直切片 (Vertical Slicing) |

## 规划概述

### 核心原则
本任务规划严格采用**垂直切片方法论**，将产品功能分解为端到端的、具有独立用户价值的功能切片。每个切片都涉及从数据库到后端API再到前端UI的所有技术层面，确保每个任务完成后都能为用户提供可用的功能价值。

### 优先级定义
- **P0 (最高优先级)**：MVP核心功能，用户能完成基本的资料分享和浏览
- **P1 (高优先级)**：完善用户体验的重要功能  
- **P2 (中优先级)**：优化和增强功能

## 任务列表

### P0 切片 - MVP核心功能

#### 1. 学科基础管理切片
**任务ID**: `56f62da7-6e48-4e29-816c-23471a5fecb3`  
**优先级**: P0  
**预计工期**: 2-3天  
**依赖关系**: 无

**功能描述**：
实现完整的学科创建和列表展示功能，为整个平台提供基础的内容分类能力。

**端到端实现**：
- **数据库层**：设计subjects表(id, name, created_at)，确保学科名称唯一性
- **后端API**：POST /api/subjects (创建学科) 和 GET /api/subjects (获取列表)
- **前端界面**：主页学科列表组件(卡片式布局) + 创建学科弹窗组件

**验收标准**：
1. 管理员能通过主页创建学科，30秒内完成操作
2. 学科名称验证正常(空值、重复、长度限制)
3. 主页以卡片形式展示所有学科，按创建时间倒序排列
4. 响应式布局在各设备上正常显示

#### 2. 单文件上传与浏览切片

**任务ID**: `cee2c71e-cbc1-49f0-99c2-b8f394bbde9f`  
**优先级**: P0  
**预计工期**: 3-4天  
**依赖关系**: 学科基础管理切片

**功能描述**：
实现基础的Markdown文件上传和在线查看功能，建立文件存储和渲染的核心机制。

**端到端实现**：
- **数据库层**：设计file_nodes表，支持树形结构存储
- **后端API**：POST /api/subjects/{id}/upload (单文件上传) 和 GET /api/files/{fileId} (获取内容)
- **前端界面**：文件上传组件 + Markdown渲染组件

**验收标准**：
1. 管理员能在学科页面上传单个.md文件
2. 上传的Markdown文件正确存储到服务器
3. 访客能在线阅读Markdown内容
4. Markdown基础语法正确渲染

#### 3. 基础文件浏览切片

**任务ID**: `0bb39134-d2fc-40cb-b1de-b2226608aabb`  
**优先级**: P0  
**预计工期**: 2-3天  
**依赖关系**: 学科基础管理切片, 单文件上传与浏览切片

**功能描述**：
实现访客浏览文件结构的功能，提供清晰的文件夹导航和文件列表展示。

**端到端实现**：
- **后端API**：GET /api/subjects/{id}/files (获取文件结构)
- **前端界面**：文件浏览器组件 + 面包屑导航组件 + 学科详情页面

**验收标准**：
1. 访客能从主页点击学科进入文件列表页面
2. 文件浏览器正确显示文件夹和文件，图标区分明确
3. 点击文件夹能进入下级目录，面包屑导航正确更新
4. 点击Markdown文件能正确跳转到内容查看页面

### P1 切片 - 完整功能

#### 4. 文件夹批量上传切片
**任务ID**: `7ed7e824-2704-4c4d-a901-8df34ff7e143`  
**优先级**: P1  
**预计工期**: 4-5天  
**依赖关系**: 学科基础管理切片, 单文件上传与浏览切片, 基础文件浏览切片

**功能描述**：
实现完整的文件夹批量上传功能，支持保持原有文件夹结构，处理多文件和多层级目录。

**端到端实现**：
- **前端实现**：使用webkitdirectory属性实现文件夹选择，批量上传处理
- **后端处理**：扩展上传API支持批量文件，重建目录结构，批量插入数据库
- **文件处理**：文件夹结构解析、路径规范化、批量文件存储

**验收标准**：
1. 管理员能选择本地文件夹进行批量上传
2. 上传后文件结构与本地100%一致
3. 支持.md、.png、.jpg、.jpeg、.gif、.svg格式
4. 单次上传限制500MB，超出时有明确提示
5. 不支持webkitdirectory的浏览器有降级提示

#### 5. 图片处理切片
**任务ID**: `777f400d-d44e-4545-88bf-d33735488f2f`  
**优先级**: P1  
**预计工期**: 3-4天  
**依赖关系**: 学科基础管理切片, 单文件上传与浏览切片, 基础文件浏览切片, 文件夹批量上传切片

**功能描述**：
实现Markdown文件中图片的正确显示，处理相对路径图片引用。

**端到端实现**：
- **后端处理**：Markdown内容解析，相对路径替换为服务器URL
- **图片存储**：建立图片文件存储和访问机制
- **路径处理**：相对路径解析算法，处理各种路径格式
- **静态资源**：配置静态文件服务

**验收标准**：
1. Markdown中相对路径图片(./images/pic.png)能正确显示
2. 支持各种相对路径格式(./、../、直接文件名)
3. 图片文件能通过HTTP正确访问
4. 不存在的图片有适当错误处理

#### 6. 响应式布局切片
**任务ID**: `cd020934-0feb-4053-94a6-a9ae5d1726b1`  
**优先级**: P1  
**预计工期**: 2-3天  
**依赖关系**: 学科基础管理切片, 单文件上传与浏览切片, 基础文件浏览切片

**功能描述**：
实现多设备适配的响应式设计，确保平台在各种设备上都有良好体验。

**端到端实现**：
- **CSS框架**：基于Ant Design Vue和UnoCSS实现响应式布局
- **布局适配**：不同设备显示不同列数，移动端优化触摸操作
- **字体间距**：根据设备调整字体大小和元素间距
- **交互优化**：移动端优化点击区域，添加触摸友好交互

**验收标准**：
1. 手机设备(<768px)单列布局显示正常
2. 平板设备(768-1024px)双列布局适配良好
3. 桌面设备(>1024px)多列布局充分利用空间
4. 移动端触摸操作流畅，点击区域合适
5. 各设备页面加载时间不超过3秒

### P2 切片 - 优化功能

#### 7. 上传进度与错误处理切片
**任务ID**: `86e254a2-3340-4462-b6e4-aa2aa2f0646b`  
**优先级**: P2  
**预计工期**: 2-3天  
**依赖关系**: 学科基础管理切片, 单文件上传与浏览切片, 文件夹批量上传切片

**功能描述**：
完善用户反馈机制，提供详细的上传进度显示和友好的错误处理。

**端到端实现**：
- **进度显示**：文件上传进度计算，显示详细上传信息
- **错误处理**：完整错误分类和处理机制
- **用户反馈**：友好的错误提示界面，具体错误原因和解决建议
- **重试机制**：网络错误等临时问题的重试功能

**验收标准**：
1. 上传过程显示详细进度信息
2. 各种错误情况有明确提示信息
3. 网络中断等临时错误提供重试功能
4. 用户能随时取消正在进行的上传操作

#### 8. 性能优化切片
**任务ID**: `fad52351-fbf2-4215-92c4-1d3c2f32b311`  
**优先级**: P2  
**预计工期**: 3-4天  
**依赖关系**: 所有前置切片

**功能描述**：
系统性能和用户体验优化，包括页面加载速度、大文件处理、缓存策略等。

**端到端实现**：
- **前端优化**：代码分割、懒加载、图片懒加载等技术
- **后端优化**：数据库查询优化、文件上传分片处理
- **缓存策略**：静态资源缓存、API响应缓存
- **性能监控**：关键性能指标监控

**验收标准**：
1. 页面首屏加载时间不超过3秒
2. 大文件(接近500MB)上传过程稳定
3. 静态资源有适当缓存策略
4. 文件列表在大量文件时加载流畅
5. 移动端性能表现良好

## 依赖关系图

```
学科基础管理切片 (P0)
    ↓
单文件上传与浏览切片 (P0)
    ↓
基础文件浏览切片 (P0)
    ↓
文件夹批量上传切片 (P1) ← 图片处理切片 (P1)
    ↓                      ↓
上传进度与错误处理切片 (P2)  ↓
    ↓                      ↓
    响应式布局切片 (P1) ←────┘
    ↓
性能优化切片 (P2)
```

## 开发排期建议

### 第一阶段 (MVP - 2周)
- 学科基础管理切片 (2-3天)
- 单文件上传与浏览切片 (3-4天)  
- 基础文件浏览切片 (2-3天)
- 集成测试和bug修复 (2-3天)

### 第二阶段 (完整功能 - 2周)
- 文件夹批量上传切片 (4-5天)
- 图片处理切片 (3-4天)
- 响应式布局切片 (2-3天)
- 集成测试和优化 (2-3天)

### 第三阶段 (优化完善 - 1周)
- 上传进度与错误处理切片 (2-3天)
- 性能优化切片 (3-4天)
- 最终测试和发布准备 (1-2天)

## 技术风险评估

### 高风险项
1. **webkitdirectory兼容性** - 需要降级方案
2. **大文件上传稳定性** - 需要分片上传机制
3. **图片路径处理复杂性** - 需要充分测试各种路径格式

### 中风险项
1. **SQLite并发性能** - 需要性能监控
2. **文件存储空间管理** - 需要清理策略

### 缓解措施
- 提前进行技术验证和原型开发
- 建立完整的测试用例覆盖
- 设置性能监控和告警机制

## 质量保证

### 测试策略
- **单元测试**：核心业务逻辑覆盖率>80%
- **集成测试**：API接口和数据库操作测试
- **端到端测试**：使用Playwright进行完整用户流程测试
- **兼容性测试**：主流浏览器和设备测试
- **性能测试**：大文件上传和高并发访问测试

### 验收标准
每个切片都有明确的验收标准，确保交付质量。所有功能必须通过完整的测试验证才能进入下一阶段。

---

**文档状态**: ✅ 已完成  
**下一步行动**: 提交给Mike审核，通过后开始技术架构设计阶段