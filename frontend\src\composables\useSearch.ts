import { ref, computed } from 'vue'
import { debounce } from 'lodash-es'

/**
 * 通用搜索功能的可复用逻辑
 * 提供搜索状态管理、防抖处理、搜索历史等功能
 */
export function useSearch<T = any>(options: {
  searchFn: (keyword: string) => Promise<{ results: T[]; total: number }>
  debounceDelay?: number
  minKeywordLength?: number
  maxHistoryItems?: number
  storageKey?: string
}) {
  const {
    searchFn,
    debounceDelay = 300,
    minKeywordLength = 2,
    maxHistoryItems = 10,
    storageKey = 'search-history'
  } = options

  // 响应式状态
  const searchKeyword = ref('')
  const isSearching = ref(false)
  const searchResults = ref<T[]>([])
  const totalResults = ref(0)
  const searchHistory = ref<string[]>([])
  const error = ref<string | null>(null)

  // 计算属性
  const hasResults = computed(() => searchResults.value.length > 0)
  const isEmpty = computed(() => !isSearching.value && searchResults.value.length === 0 && searchKeyword.value.trim().length >= minKeywordLength)

  // 搜索历史管理
  const loadHistory = () => {
    try {
      const stored = localStorage.getItem(storageKey)
      if (stored) {
        searchHistory.value = JSON.parse(stored)
      }
    } catch (err) {
      console.error('加载搜索历史失败:', err)
      searchHistory.value = []
    }
  }

  const saveToHistory = (keyword: string) => {
    if (!keyword.trim()) return

    // 移除重复项
    const filtered = searchHistory.value.filter(item => item !== keyword)
    
    // 添加到开头
    filtered.unshift(keyword)
    
    // 限制数量
    if (filtered.length > maxHistoryItems) {
      filtered.splice(maxHistoryItems)
    }
    
    searchHistory.value = filtered
    
    // 保存到本地存储
    try {
      localStorage.setItem(storageKey, JSON.stringify(searchHistory.value))
    } catch (err) {
      console.error('保存搜索历史失败:', err)
    }
  }

  const removeFromHistory = (index: number) => {
    searchHistory.value.splice(index, 1)
    try {
      localStorage.setItem(storageKey, JSON.stringify(searchHistory.value))
    } catch (err) {
      console.error('更新搜索历史失败:', err)
    }
  }

  const clearHistory = () => {
    searchHistory.value = []
    try {
      localStorage.removeItem(storageKey)
    } catch (err) {
      console.error('清除搜索历史失败:', err)
    }
  }

  // 防抖搜索函数
  const debouncedSearch = debounce(async (keyword: string) => {
    if (!keyword.trim() || keyword.length < minKeywordLength) {
      searchResults.value = []
      totalResults.value = 0
      error.value = null
      return
    }

    isSearching.value = true
    error.value = null

    try {
      const result = await searchFn(keyword.trim())
      searchResults.value = result.results
      totalResults.value = result.total
      
      // 保存搜索历史
      saveToHistory(keyword.trim())
    } catch (err: any) {
      console.error('搜索失败:', err)
      error.value = err.message || '搜索失败，请重试'
      searchResults.value = []
      totalResults.value = 0
    } finally {
      isSearching.value = false
    }
  }, debounceDelay)

  // 搜索方法
  const search = (keyword: string) => {
    searchKeyword.value = keyword
    debouncedSearch(keyword)
  }

  const searchImmediate = async (keyword: string) => {
    searchKeyword.value = keyword
    debouncedSearch.cancel()
    await debouncedSearch(keyword)
  }

  // 清除搜索
  const clearSearch = () => {
    searchKeyword.value = ''
    searchResults.value = []
    totalResults.value = 0
    error.value = null
    debouncedSearch.cancel()
  }

  // 从历史记录搜索
  const searchFromHistory = (keyword: string) => {
    search(keyword)
  }

  // 初始化
  loadHistory()

  return {
    // 状态
    searchKeyword,
    isSearching,
    searchResults,
    totalResults,
    searchHistory,
    error,
    
    // 计算属性
    hasResults,
    isEmpty,
    
    // 方法
    search,
    searchImmediate,
    clearSearch,
    searchFromHistory,
    removeFromHistory,
    clearHistory,
    
    // 内部方法（可选暴露）
    debouncedSearch
  }
}

/**
 * 文件搜索的专用hook
 */
export function useFileSearch(subjectId: number, searchApi: (subjectId: number, params: any) => Promise<any>) {
  return useSearch({
    searchFn: async (keyword: string) => {
      const response = await searchApi(subjectId, {
        q: keyword,
        limit: 20
      })
      return {
        results: response.data.data.results,
        total: response.data.data.total
      }
    },
    storageKey: `file-search-history-${subjectId}`,
    debounceDelay: 300,
    minKeywordLength: 2,
    maxHistoryItems: 10
  })
}

/**
 * 学科搜索的专用hook
 */
export function useSubjectSearch(searchFn: (keyword: string) => any[]) {
  return useSearch({
    searchFn: async (keyword: string) => {
      const results = searchFn(keyword)
      return {
        results,
        total: results.length
      }
    },
    storageKey: 'subject-search-history',
    debounceDelay: 200,
    minKeywordLength: 1,
    maxHistoryItems: 5
  })
}
