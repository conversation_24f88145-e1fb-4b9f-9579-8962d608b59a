<template>
  <div class="file-search-test-page">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">FileSearch 组件测试</h1>
          <p class="text-gray-600">测试文件搜索组件的各项功能</p>
        </div>

        <!-- 测试控制面板 -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 class="text-xl font-semibold mb-4">测试控制</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">学科ID</label>
              <a-input-number 
                v-model:value="testSubjectId" 
                :min="1" 
                placeholder="输入学科ID"
                class="w-full"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">防抖延迟 (ms)</label>
              <a-input-number 
                v-model:value="debounceDelay" 
                :min="100" 
                :max="2000"
                :step="100"
                class="w-full"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">结果限制</label>
              <a-input-number 
                v-model:value="resultLimit" 
                :min="5" 
                :max="50"
                :step="5"
                class="w-full"
              />
            </div>
          </div>
          
          <div class="mt-4 flex flex-wrap gap-2">
            <a-switch v-model:checked="showHistory" checked-children="显示历史" un-checked-children="隐藏历史" />
            <a-switch v-model:checked="showResults" checked-children="显示结果" un-checked-children="隐藏结果" />
            <a-switch v-model:checked="autoSearch" checked-children="自动搜索" un-checked-children="手动搜索" />
          </div>
        </div>

        <!-- FileSearch 组件测试区域 -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 class="text-xl font-semibold mb-4">FileSearch 组件</h2>
          
          <FileSearch
            :subject-id="testSubjectId"
            :placeholder="placeholder"
            :show-history="showHistory"
            :show-results="showResults"
            :auto-search="autoSearch"
            :debounce-delay="debounceDelay"
            :result-limit="resultLimit"
            :max-history-items="10"
            @search="handleSearch"
            @result-click="handleResultClick"
            @history-change="handleHistoryChange"
            @focus="handleFocus"
            @blur="handleBlur"
            ref="fileSearchRef"
          />
        </div>

        <!-- 事件日志 -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">事件日志</h2>
            <a-button @click="clearLogs" size="small">清除日志</a-button>
          </div>
          
          <div class="bg-gray-50 rounded p-4 max-h-60 overflow-y-auto">
            <div v-if="eventLogs.length === 0" class="text-gray-500 text-center py-4">
              暂无事件日志
            </div>
            <div
              v-for="(log, index) in eventLogs"
              :key="index"
              class="mb-2 p-2 bg-white rounded text-sm border-l-4"
              :class="getLogClass(log.type)"
            >
              <div class="flex justify-between items-start">
                <div>
                  <span class="font-medium">{{ log.type }}</span>
                  <span class="text-gray-600 ml-2">{{ log.message }}</span>
                </div>
                <span class="text-xs text-gray-400">{{ log.timestamp }}</span>
              </div>
              <div v-if="log.data" class="mt-1 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                {{ JSON.stringify(log.data, null, 2) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 功能测试按钮 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h2 class="text-xl font-semibold mb-4">功能测试</h2>
          <div class="flex flex-wrap gap-3">
            <a-button @click="testProgrammaticSearch" type="primary">
              程序化搜索
            </a-button>
            <a-button @click="testClearSearch">
              清除搜索
            </a-button>
            <a-button @click="testFocusSearch">
              聚焦搜索框
            </a-button>
            <a-button @click="testSearchHistory">
              测试搜索历史
            </a-button>
            <a-button @click="simulateError" danger>
              模拟错误
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { FileSearch } from '@/components'
import type { SearchResultItem } from '@/types'

// 测试配置
const testSubjectId = ref(1)
const debounceDelay = ref(300)
const resultLimit = ref(20)
const showHistory = ref(true)
const showResults = ref(true)
const autoSearch = ref(true)
const placeholder = ref('搜索文件和文件夹...')

// 组件引用
const fileSearchRef = ref()

// 事件日志
interface EventLog {
  type: string
  message: string
  timestamp: string
  data?: any
}

const eventLogs = ref<EventLog[]>([])

// 添加日志
const addLog = (type: string, message: string, data?: any) => {
  eventLogs.value.unshift({
    type,
    message,
    timestamp: new Date().toLocaleTimeString(),
    data
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value.splice(50)
  }
}

// 事件处理函数
const handleSearch = (keyword: string, results: SearchResultItem[]) => {
  addLog('search', `搜索关键词: "${keyword}"`, {
    keyword,
    resultCount: results.length,
    results: results.slice(0, 3) // 只显示前3个结果
  })
  message.success(`搜索完成，找到 ${results.length} 个结果`)
}

const handleResultClick = (result: SearchResultItem) => {
  addLog('resultClick', `点击搜索结果: "${result.name}"`, result)
  message.info(`点击了文件: ${result.name}`)
}

const handleHistoryChange = (history: string[]) => {
  addLog('historyChange', `搜索历史更新`, { historyCount: history.length, history })
}

const handleFocus = () => {
  addLog('focus', '搜索框获得焦点')
}

const handleBlur = () => {
  addLog('blur', '搜索框失去焦点')
}

// 工具函数
const getLogClass = (type: string) => {
  switch (type) {
    case 'search':
      return 'border-blue-400'
    case 'resultClick':
      return 'border-green-400'
    case 'historyChange':
      return 'border-yellow-400'
    case 'focus':
    case 'blur':
      return 'border-gray-400'
    default:
      return 'border-gray-300'
  }
}

const clearLogs = () => {
  eventLogs.value = []
  message.success('日志已清除')
}

// 测试功能
const testProgrammaticSearch = () => {
  const keywords = ['test', 'markdown', '复习', '笔记', 'README']
  const keyword = keywords[Math.floor(Math.random() * keywords.length)]
  
  if (fileSearchRef.value) {
    fileSearchRef.value.search(keyword)
    addLog('test', `程序化搜索: "${keyword}"`)
  }
}

const testClearSearch = () => {
  if (fileSearchRef.value) {
    fileSearchRef.value.clear()
    addLog('test', '清除搜索')
  }
}

const testFocusSearch = () => {
  if (fileSearchRef.value) {
    fileSearchRef.value.focus()
    addLog('test', '聚焦搜索框')
  }
}

const testSearchHistory = () => {
  const testKeywords = ['历史测试1', '历史测试2', '历史测试3']
  testKeywords.forEach((keyword, index) => {
    setTimeout(() => {
      if (fileSearchRef.value) {
        fileSearchRef.value.search(keyword)
      }
    }, index * 1000)
  })
  addLog('test', '测试搜索历史功能')
}

const simulateError = () => {
  // 使用无效的学科ID来模拟错误
  const originalId = testSubjectId.value
  testSubjectId.value = 99999
  
  setTimeout(() => {
    if (fileSearchRef.value) {
      fileSearchRef.value.search('error-test')
    }
    
    // 恢复原始ID
    setTimeout(() => {
      testSubjectId.value = originalId
    }, 2000)
  }, 100)
  
  addLog('test', '模拟搜索错误')
}

onMounted(() => {
  addLog('system', '页面加载完成，FileSearch组件已初始化')
})
</script>

<style scoped>
.file-search-test-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
}
</style>
